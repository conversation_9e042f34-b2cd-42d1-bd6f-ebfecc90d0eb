/**
 * This file was auto-generated by openapi-typescript.
 * Do not make direct changes to the file.
 */


export interface paths {
  "/music_club/create": {
    /** 百相演奏-乐团-创建  (服务端调用) */
    post: operations["musicClubCreate"];
  };
  "/music_club/disband": {
    /** 百相演奏-乐团-解散  (服务端调用) */
    post: operations["musicClubDisband"];
  };
  "/music_club/show": {
    /**
     * 百相演奏-乐团-详情
     * @description 获取乐团详情, 返回主打唱片，当前上架唱片数量等信息，注意，这里不校验乐团id，因为服务器请求是，乐团信息可能未同步完成，此时返回符合数据结构的空信息
     */
    get: operations["musicClubShow"];
  };
  "/music_club/update": {
    /** 百相演奏-乐团-更新 (服务端调用) */
    post: operations["musicClubUpdate"];
  };
  "/music_club/rank/hot_list": {
    /**
     * 百相演奏-排行-最热乐团总榜
     * @description 总热度榜：展示50名。总热度从高到低排序。
     */
    get: operations["getMusicClubRankHotList"];
  };
  "/music_club/server/rank/hot_list": {
    /**
     * 百相演奏-排行-最热乐团总榜 (服务端调用)
     * @description 总热度榜：展示10名。总热度从高到低排序。
     */
    get: operations["getMusicClubRankHotListForServer"];
  };
  "/music_club/rank/week_hot_list": {
    /**
     * 百相演奏-排行-新晋乐团榜单
     * @description 新晋榜：展示20名。本周（从周一零点开始算）获取的总热度值最高的队伍。
     */
    get: operations["getMusicClubRankWeekHotList"];
  };
  "/music_club/server/rank/week_hot_list": {
    /**
     * 百相演奏-排行-新晋乐团榜单 (服务端调用)
     * @description 新晋榜：展示10名。当前周weekDs（从周一零点开始算）获取的总热度值最高的队伍。
     */
    get: operations["getMusicClubRankWeekHotListForServer"];
  };
  "/music_club/radio/recording_list": {
    /**
     * 百相演奏-点播-唱片列表
     * @description 展示唱片列表，用于游戏内点播节目的信息展示
     */
    get: operations["getMusicClubRecordingList"];
  };
  "/music_club/radio/request_play": {
    /**
     * 百相演奏-点播-同步点播操作行为 (服务端调用)
     * @description 游戏同步点播操作行为，用于维护增加唱片点播计数
     */
    post: operations["musicClubRadioRequestPlay"];
  };
  "/music_club/recording/audit_callback": {
    /**
     * 唱片审核回调
     * @description 外部审核系统完成审核后调用此接口通知审核结果
     */
    post: operations["musicClubRecordingAuditCallback"];
  };
  "/music_club/recording/release": {
    /**
     * 百相演奏-唱片-上架
     * @description 上架唱片，从本地唱片上架到乐团
     */
    post: operations["musicClubRecordingRelease"];
  };
  "/music_club/recording/remove": {
    /**
     * 百相演奏-唱片-下架
     * @description 下架唱片，从乐团中移除该唱片, 只有乐团经理可以下架唱片, 需要*游戏服务端调用*, 完成乐团经理的权限校验
     */
    post: operations["musicClubRecordingRemove"];
  };
  "/music_club/recording/rate": {
    /**
     * 百相演奏-唱片-打分 (服务端接口)
     * @description 给唱片打分，打分范围0-10分
     */
    post: operations["musicClubRecordingRate"];
  };
  "/music_club/server/recording/show": {
    /**
     * 百相演奏-唱片-详情 (服务端接口)
     * @description 获取唱片详细信息，包括乐团信息、点播次数、评分等
     */
    get: operations["musicClubServerRecordingShow"];
  };
  "/music_club/recording/show": {
    /**
     * 百相演奏-唱片-详情
     * @description 获取唱片详细信息，包括乐团信息、点播次数、评分等
     */
    get: operations["musicClubRecordingShow"];
  };
  "/kafka/music_club/BandHeat": {
    /**
     * 乐团-热度变化日志
     * @description 处理来自游戏的乐团热度变化日志，用于更新乐团总榜单和周榜单。
     *
     * 日志来源：杨凯 游戏程序 <EMAIL>
     *
     * 日志格式示例：
     * ```
     * [2025-06-25 21:55:20][BandHeat],{"server":"1", "band_id":"300001", "band_name":"yk的乐团", "band_level":3, "heat":60, "week_heat":50, "time":1750859718, "show_amount":10, "recording_amount":0, "u_dtls":[]}
     * ```
     */
    post: operations["kafkaMusicClubBandHeat"];
  };
  "/kafka/CreditScore": {
    /** 信用分日志 */
    post: operations["kafkaCreditscore"];
  };
  "/kafka/LoginRole_Additional": {
    /** 角色登录额外信息日志 */
    post: operations["kafkaLoginroleAdditional"];
  };
  "/kafka/PlayerLevelUp": {
    /** 玩家升级日志 */
    post: operations["kafkaPlayerlevelup"];
  };
  "/healthCheck": {
    /** 健康检查 */
    get: operations["healthCheck"];
  };
  "/moment/tags": {
    /** 获取动态允许的标签id列表 */
    get: operations["momentTags"];
  };
  "/at_players/list": {
    /** 玩家动态at列表 */
    get: operations["atPlayersList"];
  };
  "/at_players/search": {
    /** 玩家动态at玩家搜索 */
    get: operations["atPlayersSearch"];
  };
  "/fashion_lottery/list": {
    /** 时装抽奖获取记录列表 */
    get: operations["fashionLotteryList"];
  };
  "/fashion_lottery/long_term_list": {
    /** 时装抽奖长期类型获取记录列表 */
    get: operations["fashionLotteryLongTermList"];
  };
  "/face_pinch/web/public/share_info": {
    /**
     * 捏脸-分享-信息
     * @description 捏脸站作品公开的分享信息
     */
    get: operations["facePinchWebPublicShareInfo"];
  };
  "/face_pinch/web/public/share_video/info": {
    /**
     * 捏脸-视频-分享信息
     * @description 游戏内捏脸视频分享信息
     */
    get: operations["facePinchWebPublicShareVideoInfo"];
  };
  "/face_pinch/work/list_public": {
    /**
     * 捏脸-作品-公开列表
     * @description 捏脸站作品公开列表
     */
    get: operations["facePinchWorkListPublic"];
  };
  "/face_pinch/work/list_collect": {
    /**
     * 捏脸-作品-收藏列表
     * @description 捏脸站作品玩家自己收藏列表
     */
    get: operations["facePinchWorkListCollect"];
  };
  "/face_pinch/work/list_self": {
    /**
     * 捏脸-作品-个人列表
     * @description 捏脸站作品玩家自己的设计作品
     */
    get: operations["facePinchWorkListSelf"];
  };
  "/face_pinch/work/get_by_share_id": {
    /**
     * 捏脸-作品-分享获取
     * @description 通过分享id获取作品
     */
    get: operations["facePinchWorkGetByShareId"];
  };
  "/face_pinch/work/detail": {
    /**
     * 捏脸-作品-详情
     * @description 查看单个作品
     */
    get: operations["facePinchWorkDetail"];
  };
  "/face_pinch/work/update_visibility": {
    /**
     * 捏脸-作品-更新可见性
     * @description 修改作品的可见状态
     */
    post: operations["facePinchWorkUpdateVisibility"];
  };
  "/face_pinch/get_fp_token": {
    /**
     * 捏脸-FP令牌-获取
     * @description 获取捏脸站fp上传token
     */
    get: operations["facePinchGetFpToken"];
  };
  "/face_pinch/work/search": {
    /**
     * 捏脸-作品-搜索
     * @description 支持玩家搜索到所有已被公开的作品，不限制职业和性别，但不包含仅个人可见的作品。
     */
    get: operations["facePinchWorkSearch"];
  };
  "/face_pinch/cloud_game/auth/login": {
    /**
     * 捏脸-云游戏-登录
     * @description 云游戏捏脸相关接口登录, 获取skey接口凭证
     * - account使用云游戏登录的账号字段, roleid无可传0,
     * - token计算依赖的 FACE_PINCH_CLOUD_AUTH_SALT 联系 <EMAIL> 获取
     */
    post: operations["facePinchCloudGameAuthLogin"];
  };
  "/face_pinch/dashen/share_info/import": {
    /**
     * 捏脸-数据-导入
     * @description 导入捏脸数据到指定roleId
     */
    post: operations["facePinchDashenShareInfoImport"];
  };
  "/face_pinch/work/add": {
    /**
     * 捏脸-作品-新增
     * @description 新增一个玩家的捏脸作品数据
     */
    post: operations["facePinchWorkAdd"];
  };
  "/face_pinch/share_video/add": {
    /**
     * 捏脸-视频-新增分享
     * @description 给分享视频生成一个分享id
     */
    post: operations["facePinchShareVideoAdd"];
  };
  "/face_pinch/work/del": {
    /**
     * 捏脸-作品-删除
     * @description 删除一个玩家的捏脸作品数据
     */
    post: operations["facePinchWorkDel"];
  };
  "/face_pinch/work/apply": {
    /**
     * 捏脸-作品-应用
     * @description 应用该捏脸作品, 用于捏脸作品被应用于预创建计数(热度计算因子之一)
     */
    post: operations["facePinchWorkApply"];
  };
  "/face_pinch/work/like": {
    /**
     * 捏脸-作品-点赞
     * @description 点赞捏脸作品
     */
    post: operations["facePinchWorkLike"];
  };
  "/face_pinch/work/cancel_like": {
    /**
     * 捏脸-作品-取消点赞
     * @description 取消点赞捏脸作品
     */
    post: operations["facePinchWorkCancelLike"];
  };
  "/face_pinch/work/collect": {
    /**
     * 捏脸-作品-收藏
     * @description 收藏捏脸作品
     */
    post: operations["facePinchWorkCollect"];
  };
  "/face_pinch/work/cancel_collect": {
    /**
     * 捏脸-作品-取消收藏
     * @description 取消收藏捏脸作品
     */
    post: operations["facePinchWorkCancelCollect"];
  };
  "/player/expression_base/update": {
    /** 更新玩家的头像框 */
    post: operations["playerExpressionBaseUpdate"];
  };
  "/this_day_that_year/moment_has": {
    /**
     * 那年今日是否发表过动态
     * @description 玩家在往年的同一日期至少发布过一条动态的时候返回为true
     */
    post: operations["thisDayThatYearMomentHas"];
  };
  "/this_day_that_year/moment_list": {
    /**
     * 那年今日的动态列表
     * @description 返回那年同一日的动态列表，按照时间倒序
     */
    post: operations["thisDayThatYearMomentList"];
  };
  "/complain/add": {
    /** 添加举报日志 */
    post: operations["complainAdd"];
  };
  "/equip/comments/create": {
    /**
     * 添加兵器谱弹幕
     * @description 发言限制: *每个玩家每个武器1分钟限制1条弹幕*
     */
    post: operations["equipCommentsCreate"];
  };
  "/equip/comments/list": {
    /** 兵器谱弹幕装备列表 */
    post: operations["equipCommentsList"];
  };
  "/equip/weapons/filters": {
    /** 获取兵器谱排行筛选数据 */
    get: operations["equipWeaponsFilters"];
  };
  "/equip/weapons/rank": {
    /** 获取兵器谱排行 */
    get: operations["equipWeaponsRank"];
  };
  "/equip/treasures/rank": {
    /** 获取奇珍榜排行 */
    get: operations["equipTreasuresRank"];
  };
  "/card/is_enable": {
    /** 剧情卡功能是否对玩家开放 */
    post: operations["cardIsEnable"];
  };
  "/card/notification/list": {
    /** 卡片通知列表 */
    post: operations["cardNotificationList"];
  };
  "/card/notification/del_all": {
    /** 卡片通知列表全部清空 */
    post: operations["cardNotificationDelAll"];
  };
  "/card/notification/new_num": {
    /** 卡片通知新消息数量 */
    post: operations["cardNotificationNewNum"];
  };
  "/card/notification/read_all": {
    /**
     * 卡片通知列表全部设为已读
     * @deprecated
     */
    post: operations["cardNotificationReadAll"];
  };
  "/card/notification/read": {
    /**
     * 卡片通知列表单条已读
     * @deprecated
     */
    post: operations["cardNotificationRead"];
  };
  "/card/notion/comment/list": {
    /** 列出卡片想法评论列表 */
    post: operations["cardNotionCommentList"];
  };
  "/card/notion/comment/add": {
    /** 添加卡片想法评论 */
    post: operations["cardNotionCommentAdd"];
  };
  "/card/notion/comment/del": {
    /** 删除卡片想法评论 */
    post: operations["cardNotionCommentDel"];
  };
  "/card/notion/show": {
    /** 列出单个卡片的想法详情 */
    post: operations["cardNotionShow"];
  };
  "/card/notion/list": {
    /** 列出单个卡片下想法列表 */
    post: operations["cardNotionList"];
  };
  "/card/notion/add": {
    /** 添加卡片的想法 */
    post: operations["cardNotionAdd"];
  };
  "/card/notion/del": {
    /** 删除卡片的想法 */
    post: operations["cardNotionDel"];
  };
  "/card/notion/like": {
    /** 给卡片想法点赞 */
    post: operations["cardNotionLike"];
  };
  "/card/notion/cancel_like": {
    /** 给卡片想法取消点赞 */
    post: operations["cardNotionCancelLike"];
  };
  "/card/list": {
    /** 卡片列表统计详情 */
    post: operations["cardList"];
  };
  "/card/red_dot": {
    /** 卡片红点接口(返回所有有新消息的卡片id) */
    post: operations["cardRedDot"];
  };
  "/card/like": {
    /** 点赞卡片 */
    post: operations["cardLike"];
  };
  "/card/cancel_like": {
    /** 取消点赞卡片 */
    post: operations["cardCancelLike"];
  };
  "/slient_love/add": {
    /** 添加对方为暗恋 */
    post: operations["slientLoveAdd"];
  };
  "/slient_love/cancel": {
    /** 取消暗恋 */
    post: operations["slientLoveCancel"];
  };
  "/filepick/get_token": {
    /** 获取filepick的token */
    post: operations["filepickGetToken"];
  };
  "/info/is_enable_island": {
    /** 查询是否开启梦岛 */
    post: operations["infoIsEnableIsland"];
  };
  "/chat/photo/get": {
    /** 获取聊天图片 */
    post: operations["chatPhotoGet"];
  };
  "/chat/photo/create": {
    /** 保存聊天图片 */
    post: operations["chatPhotoCreate"];
  };
  "/chat/nos/gettoken": {
    /** 获取上传聊天图片nosToken */
    post: operations["chatNosGettoken"];
  };
  "/chat/nos/get_free_token": {
    /** 获取上传聊天图片免审核nosToken */
    post: operations["chatNosGetFreeToken"];
  };
  "/chat/collect_photo/add": {
    /** 添加图片到收藏 */
    post: operations["chatCollectPhotoAdd"];
  };
  "/chat/collect_photo/list": {
    /** 图片收藏列表 */
    post: operations["chatCollectPhotoList"];
  };
  "/chat/collect_photo/remove": {
    /** 从收藏删除图片 */
    post: operations["chatCollectPhotoRemove"];
  };
  "/activity/:activity_name/share": {
    /** 官网活动分享 */
    post: operations["activity:activityNameShare"];
  };
  "/activity/ndzj2020/moment/most_liked": {
    /** 年度总结2020点赞最多动态 */
    get: operations["activityNdzj2020MomentMostLiked"];
  };
  "/activity/ndzj2020/wish/help_rank": {
    /** 年度总结2020助力心愿助力排行 */
    get: operations["activityNdzj2020WishHelpRank"];
  };
  "/activity/ndzj2020/wish/recent_helps": {
    /** 年度总结2020心最近助力心愿列表 */
    get: operations["activityNdzj2020WishRecentHelps"];
  };
  "/activity/login": {
    /** 活动自动登录 */
    get: operations["activityLogin"];
  };
  "/activity/login/bridge": {
    /** 活动自动登录中转页(jsBridge) */
    get: operations["activityLoginBridge"];
  };
  "/activity/login/validate_access_token": {
    /** 验证自动登录产生的token并获取相关信息 */
    get: operations["activityLoginValidateAccessToken"];
  };
  "/activity/add_moment": {
    /**
     * 活动分享到梦岛
     * @description ## 10参与话题是个特殊的文本链接
     * `<link button=笛子特效,PSHotTalk,12>话题+文案+表情#24#28` <br>
     * `<link button=笛子特效,PSHotTalk,12>`<br>
     * `<link button={话题名字},PSHotTalk,{话题id}>` <br>
     * 按照这个规则来生成， 话题id要等话题管理后台上线了话题才有的。 这两个变量建议放在配置中
     */
    post: operations["activityAddMoment"];
  };
  "/activity/qmpk/rank": {
    /** 全面PK排行 */
    post: operations["activityQmpkRank"];
  };
  "/activity/qmpk/rank2": {
    /** 全面PK排行(PHP后台访问, ip校验) */
    get: operations["activityQmpkRank2"];
  };
  "/gm_cmd/inform/unread_all": {
    /**
     * GM指令-通知-标记所有未读
     * @description 通过玩家ID将该玩家的所有通知状态设置为未读
     */
    post: operations["markAllInformUnread"];
  };
  "/gm_cmd/moment/search": {
    /** GM指令-查找玩家的某条动态 */
    post: operations["gmCmdMomentSearch"];
  };
  "/gm_cmd/moment_lottery/draw": {
    /** GM指令-抽奖动态触发开奖 */
    post: operations["gmCmdMomentLotteryDraw"];
  };
  "/gm_cmd/moment_lottery/attend": {
    /** GM指令-用于测试互动后，是否参与成功 */
    post: operations["gmCmdMomentLotteryAttend"];
  };
  "/gm_cmd/gms/postReceiveLimit.php": {
    /**
     * GM指令-抽奖动态发奖指令
     * @description 手动触发发奖，调用游戏发奖指令, 纯代理，保持上游接口风格
     */
    post: operations["gmCmdGmsPostreceivelimit.php"];
  };
  "/gm_cmd/qmpk/set_rank": {
    /** 设置全面争霸排行名次(只在测试环境可用) */
    post: operations["gmCmdQmpkSetRank"];
  };
  "/gm_cmd/hotmoment/refresh": {
    /**
     * GM指令-热门动态-刷新热门
     * @description 更新热门动态缓存, 传入serverIds和tagIds, tagIds和serverIds都可以选择多个
     */
    post: operations["gmCmdUpdateHotMomentsCache"];
  };
  "/activity/qmpk/share": {
    /** 全面PK分享 */
    post: operations["activityQmpkShare"];
  };
  "/auth/login": {
    /**
     * 角色登录梦岛
     * @description 角色登录梦岛, 获取接口访问凭证
     */
    post: operations["authLogin"];
  };
  "/server/player/setProtectMode": {
    /** 梦岛设置好友可见 */
    post: operations["serverPlayerSetProtectMode"];
  };
  "/daily_login/login_time": {
    /** 获取每日登录时间(只支持7d之内) */
    get: operations["dailyLoginLoginTime"];
  };
  "/auth/mark_role_status": {
    /**
     * 标记游戏角色状态
     * @description 验证参数和方式同梦岛登录接口
     */
    post: operations["authMarkRoleStatus"];
  };
  "/activity/get_player_moments": {
    /** 获取玩家心情列表 */
    get: operations["activityGetPlayerMoments"];
  };
  "/activity/survey/submit": {
    /** 问卷系统回调接口 */
    post: operations["activitySurveySubmit"];
  };
  "/getallinforms": {
    /**
     * 通知-全部列表
     * @description 获取全部通知
     */
    post: {
      parameters: {
        query: {
          roleid: components["parameters"]["roleid"];
          lastid: components["parameters"]["lastid2"];
          pagesize?: components["parameters"]["inform-pagesize"];
        };
      };
      responses: {
        200: components["responses"]["res-inform-list"];
      };
    };
  };
  "/getinforms": {
    /**
     * 通知-未读列表
     * @description 获取未读通知列表
     */
    post: {
      parameters: {
        query: {
          roleid: components["parameters"]["roleid"];
          lastid: components["parameters"]["lastid2"];
          pagesize?: components["parameters"]["inform-pagesize"];
        };
      };
      responses: {
        200: components["responses"]["res-inform-list"];
      };
    };
  };
  "/getnewsnum": {
    /**
     * 通知-新消息数量
     * @description 获取新消息数量
     */
    post: {
      parameters: {
        query: {
          roleid: components["parameters"]["roleid"];
          /** @description 是否返回最新的一条未读消息 */
          include?: "newinform";
        };
      };
      responses: {
        /** @description ok */
        200: components["responses"]["res-inform-news-num"];
      };
    };
  };
  "/informs/read_all": {
    /**
     * 通知-标记所有消息为已读
     * @description 将指定用户的所有通知标记为已读，并返回标记的消息数量
     */
    post: {
      parameters: {
        query: {
          roleid: components["parameters"]["roleid"];
        };
      };
      responses: {
        /** @description 成功标记所有通知为已读 */
        200: {
          content: {
            "application/json": {
              /** @example 0 */
              code?: number;
              data?: {
                /**
                 * @description 标记为已读的消息数量
                 * @example 10
                 */
                markedCount?: number;
              };
            };
          };
        };
      };
    };
  };
  "/addmessage": {
    /** @description 添加留言 */
    post: operations["addmessage"];
  };
  "/getprofile": {
    /** 获取玩家信息详情 */
    post: operations["getprofile"];
  };
  "/clean_account": {
    /**
     * 清空梦岛所有信息(服务器调用)
     * @description 需要删除的内容为：我的梦岛，心愿，留言板，曾用名（曾用名应该是游戏这边的）, token计算方式和 auth/login 相同
     */
    post: operations["cleanAccount"];
  };
  "/background/syncId": {
    /** 同步玩家的背景图片(服务端) */
    post: operations["backgroundSyncid"];
  };
  "/getevents": {
    /** 获取互动事件列表 */
    post: operations["getevents"];
  };
  "/addevent": {
    /** 添加互动事件 */
    post: operations["addevent"];
  };
  "/info/get_locations": {
    /** 获取地理位置(客户端家谱) */
    post: operations["infoGetLocations"];
  };
  "/info/get_avatar": {
    /** 获取玩家头像 */
    post: operations["infoGetAvatar"];
  };
  "/location/nearby/players": {
    /** 附近的玩家 */
    post: operations["locationNearbyPlayers"];
  };
  "/auth/login_by_ds": {
    /** 大神web端登录 */
    post: operations["authLoginByDs"];
  };
  "/getmoments": {
    /** 获取用户朋友圈或者指定用户的朋友圈 */
    post: operations["listMoment"];
  };
  "/moment/add_article": {
    /**
     * 发表朋友圈长文
     * @deprecated
     */
    post: operations["momentAddArticle"];
  };
  "/getmomentbyid": {
    /** 动态-通过id获取动态详情 */
    post: operations["getMomentById"];
  };
  "/moment/listByGuild": {
    /** 动态-查看本帮 */
    post: operations["listMomentByGuild"];
  };
  "/addmoment": {
    /**
     * 添加动态
     * @description 话题特殊文本格式b2例子 `<link button=coser活动,PSHotTalk,2>`
     * @玩家功能富文本格式例子  `<link button=太刀川庆,AtPlayer,281851200548>` 代表@281851200548这个玩家
     */
    post: operations["addMoment"];
  };
  "/moment/forward": {
    /** 转发动态 */
    post: operations["momentForward"];
  };
  "/delmoment": {
    /** 删除动态 */
    post: operations["delMoment"];
  };
  "/gethotmoments": {
    /** 热门动态-本服热门 */
    post: operations["gethotmoments"];
  };
  "/getallhotmoments": {
    /** 热门动态-全服热门 */
    post: operations["getallhotmoments"];
  };
  "/comment/more": {
    /** 获取更多评论 */
    post: operations["commentMore"];
  };
  "/comment/list": {
    /** 评论列表 */
    get: operations["commentList"];
  };
  "/addcomment": {
    /** 添加评论或回复 */
    post: operations["addcomment"];
  };
  "/delcomment": {
    /** 删除评论 */
    post: operations["delcomment"];
  };
  "/likemoment": {
    /** 点赞心情 */
    post: operations["likemoment"];
  };
  "/follow/add": {
    /**
     * 关注
     * @description |ErrorCode| Message|
     * |---| ---|
     * |-1000 | 到达关注上限|
     */
    post: operations["followAdd"];
  };
  "/follow/cancel": {
    /** 取消关注 */
    post: operations["followCancel"];
  };
  "/follow/cancel_batch": {
    /** 取消关注 */
    post: operations["followCancelBatch"];
  };
  "/follow/follow_list": {
    /** 关注列表 */
    post: operations["followFollowList"];
  };
  "/following": {
    /** 关注列表 */
    post: operations["following"];
  };
  "/followers": {
    /** 粉丝列表 */
    post: operations["followers"];
  };
  "/getlocation": {
    /** 梦岛个人信息左侧自动获取地理位置依赖的接口 */
    post: operations["getlocation"];
  };
  "/common/ip2location": {
    /** 根据ip查询ip库返回地理位置 */
    get: operations["commonIp2location"];
  };
  "/nos/gettoken": {
    /** 获取nosToken */
    get: operations["nosGettoken"];
  };
  "/topic/list": {
    /** 活动话题列表 */
    post: operations["topicList"];
  };
  "/getrank": {
    /** 获取排行 */
    post: operations["getrank"];
  };
  "/topic/moment/list": {
    /** 列出话题心情 */
    post: operations["topicMomentList"];
  };
  "/topic_admin/add": {
    /** 添加话题 */
    post: operations["topicAdminAdd"];
  };
  "/topic_admin/update_status": {
    /** 删除话题 */
    post: operations["topicAdminUpdateStatus"];
  };
  "/topic_admin/list": {
    /** 话题列表 */
    get: operations["topicAdminList"];
  };
  "/open/getNosToken/{filename}/{extname}": {
    /** 获取nosToken(开放接口) */
    get: operations["openGetnostoken"];
  };
  "/fuxi/getNosToken": {
    /** 获取Nos上传Token */
    get: operations["fuxiGetnostoken"];
  };
  "/gm_cmd/job_avatar/upload": {
    /** 上传官网职业头像 */
    post: operations["gmCmdJobAvatarUpload"];
  };
  "/wishlist/add": {
    /** 新增心愿 */
    post: operations["wishlistAdd"];
  };
  "/wishlist/addHelps": {
    /** 助力心愿单 */
    post: operations["wishlistAddhelps"];
  };
  "/wishlist/updateStatus": {
    /**
     * 修改状态
     * @description status:
     *   ```
     *   enum EWishStatus {
     *       Default = 0, //默认，玩家可以助力、删除等操作
     *     Reward = 1, //已领奖
     *     Delete = 2, //已删除
     *     Return = 3, //已返还
     *   }
     *   ```
     * visibility:
     *   ```
     *   enum EVisibility {
     *       All = 0, //所有人可见
     *       None = 1, //无人可见
     *       Owner = 2  //自己可见
     *   }
     *   ```
     * status与isHide参数至少传递其中一个参数
     */
    post: operations["wishlistUpdatestatus"];
  };
  "/wishlist/syncOne": {
    /** 同步心愿单 */
    post: operations["wishlistSyncone"];
  };
  "/wishlist/listByRole": {
    /** 获取某人心愿单 */
    post: operations["wishlistListbyrole"];
  };
  "/wishlist/list": {
    /** 获取自己和朋友的心愿单 */
    post: operations["wishlistList"];
  };
  "/wishlist/detail": {
    /** 查看某条心愿单 */
    post: operations["wishlistDetail"];
  };
  "/wishlist/help_text/add": {
    /** 添加文本类心愿助力 */
    post: operations["wishlistHelpTextAdd"];
  };
  "/wishlist/help_text/del": {
    /** 删除文本类心愿助力 */
    post: operations["wishlistHelpTextDel"];
  };
  "/firework_photo/update": {
    /** 更新指定位置的烟花图片 */
    post: operations["fireworkPhotoUpdate"];
  };
  "/firework_photo/remove": {
    /** 删除指定位置的图片 */
    post: operations["fireworkPhotoRemove"];
  };
  "/firework_photo/list": {
    /** 玩家烟花图片列表(10张) */
    get: operations["fireworkPhotoList"];
  };
  "/firework_photo/get_by_ids": {
    /** 通过图片id列表获取图片 */
    get: operations["fireworkPhotoGetByIds"];
  };
  "/home_decorate_photo/update": {
    /** 更新指定位置的家园自定义装饰图片 */
    post: operations["homeDecoratePhotoUpdate"];
  };
  "/home_decorate_photo/remove": {
    /** 删除指定位置的图片 */
    post: operations["homeDecoratePhotoRemove"];
  };
  "/home_decorate_photo/list": {
    /** 玩家庄园自定义图片列表(10张) */
    get: operations["homeDecoratePhotoList"];
  };
  "/home_decorate_photo/get_by_ids": {
    /** 通过图片id列表获取图片 */
    get: operations["homeDecoratePhotoGetByIds"];
  };
  "/report_log/get_nos_token": {
    /** 获取上传玩家日志文件NosToken */
    get: operations["getReportLogNosToken"];
  };
  "/report_log/add": {
    /** 添加设备汇报日志文件 */
    post: operations["addReportLog"];
  };
  "/report_log/list": {
    /** 玩家日志文件列表检索 */
    get: operations["listReportLog"];
  };
  "/report_log/allow_device/list": {
    /** 列出允许设备名单列表 */
    get: operations["listAllowDevice"];
  };
  "/report_log/allow_device/add": {
    /** 添加设备到允许名单 */
    post: operations["addAllowDevice"];
  };
  "/report_log/allow_device/remove": {
    /** 从允许名单中删除设备 */
    post: operations["removeAllowDevice"];
  };
  "/server/moment_lottery/add": {
    /**
     * 动态抽奖-添加
     * @description 动态抽奖-添加, **游戏服务器调用**
     */
    post: operations["serverMomentLotteryAdd"];
  };
  "/moment_lottery/show": {
    /**
     * 动态抽奖-展示
     * @description 动态抽奖-展示, 用于包含动态抽奖的动态展示当前的抽奖信息
     */
    get: operations["momentLotteryShow"];
  };
  "/moment_lottery/winners": {
    /** 动态抽奖-中奖玩家名单 */
    get: operations["momentLotteryWinners"];
  };
  "/server/transfer/add": {
    /** 添加转服记录, 并完成所有RoleId相关数据的迁移 */
    post: operations["serverTransferAdd"];
  };
  "/server/fpFileReviewCallback": {
    /** filePicker文件审核回调 */
    post: operations["serverFpFileReviewCallback"];
  };
}

export type webhooks = Record<string, never>;

export interface components {
  schemas: {
    MomentLotteryWinner: {
      /** @example 24017600001 */
      roleId: number;
      /** @example 中奖的玩家名 */
      roleName: string;
      /** @example 1 */
      serverId: number;
      /** @example 服务器名 */
      serverName: string;
    };
    MomentLotteryWinnersData: {
      /** @example 15938804987 */
      momentId: number;
      winners: components["schemas"]["MomentLotteryWinner"][];
      /**
       * @description 中奖人数
       * @example 100
       */
      winnerNum: number;
      hostPlayer: {
        /** @example 24017600001 */
        roleId: number;
        /** @example 发起抽奖的玩家名 */
        roleName: string;
        /** @example 1 */
        serverId: number;
        /** @example 服务器名 */
        serverName: string;
      };
      /** @description 开奖时间(ms) */
      drawTime: number;
      /**
       * @description 抽奖状态 0 未开奖 1 已开奖 2 已取消
       * @example 1
       */
      drawStatus: number;
    };
    ServerMomentLotteryAddResp: {
      /**
       * @description 动态id
       * @example 1
       */
      id?: number;
      /**
       * @description 抽奖动态的唯一id
       * @example lottery_sn
       */
      lotterySn?: string;
    };
    DailyLoginLoginTime: {
      /**
       * @description 角色id
       * @example 100100001
       */
      roleid?: number;
      /**
       * @description 当日最新一次登录时间
       * @example 1723639944885
       */
      loginTime?: number;
    };
    /**
     * @description 动态抽奖礼品(是指单份，就是中奖者能获取的奖励), 格式为 <道具id>,<数量>,<价格>;<道具id>,<数量>,<价格>
     * @example 21000039,1,9;21000040,2,30
     */
    MomentLotteryPrize: string;
    MomentLotteryAdd: {
      /** @description 每条抽奖动态的唯一id, 用于保证幂等 */
      sn: string;
      /**
       * @description 抽奖类型 1:阳光普照 2:天选之人
       * @example 1
       * @enum {integer}
       */
      type: 1 | 2;
      prizes: components["schemas"]["MomentLotteryPrize"];
      /** @description 所需的灵玉数量 */
      jade: number;
      /**
       * @description 中奖人数
       * @example 10
       */
      winnerNum?: number;
      /** @description 开奖时间, 单位秒 */
      drawTime: number;
      /**
       * @description 参与抽奖范围, 全服或者本服
       * @enum {string}
       */
      serverScope: "all" | "local";
      requirements: components["schemas"]["moment-lottery-requirements"];
      /**
       * @description 参与抽奖的最小参与等级, 0代表不开启等级下限
       * @default 0
       */
      minLevel: number;
    };
    ServerMomentLotteryAddReq: components["schemas"]["MomentAddRequestBody"] & {
      lottery: components["schemas"]["MomentLotteryAdd"];
    } & {
      /**
       * @description 时间参数, 单位秒
       * @example 1599725110
       */
      time: number;
      /**
       * @description md5(time+roleid+sn+salt)
       * @example 086779eda50242b2af78d79e5991ca78
       */
      token: string;
    };
    ServerTransferAddData: {
      /**
       * @description 转服任务id (提供查询转服任务进度)
       * @example 1
       */
      taskId?: number;
    };
    MomentTagList: {
      list: components["schemas"]["MomentTagItem"][];
    };
    AtPlayersList: {
      list: components["schemas"]["AtPlayersItem"][];
      /** @example 50 */
      count: number;
    };
    MomentTagItem: {
      /** @example 1 */
      id: number;
      /**
       * @description 标签名
       * @example 家园
       */
      name: string;
    };
    AtPlayersItem: {
      /** @example 42448100244 */
      roleId: number;
      /**
       * @description 角色名
       * @example 天天不猜
       */
      roleName: string;
      /** @example 2 */
      serverId: number;
      /** @example 10 */
      level: number;
      /** @example 10 */
      jobId: number;
      /** @example 0 */
      gender: number;
    };
    FashionLotteryList: {
      list: components["schemas"]["FashionLottery"][];
      /** @example 50 */
      count: number;
    };
    FashionLottery: {
      /** @example 1 */
      id: number;
      /** @example 42448100244 */
      roleId: number;
      /**
       * @description 奖品等级,  0 => 精致，1 => 豪华，2 =>至尊
       * @example 1
       */
      itemLevel: number;
      /**
       * @description 获奖编号
       * @example 3
       */
      itemIndex: number;
      /** @example 1617179813989 */
      receiveTime: number;
    };
    FacePinchGetFpToken: {
      /** @example Policy F0nfg+CY6PrcVQ9rV5ZwGuWJMnw=:eyJmc2l6ZUxpbWl0IjpbMCwyMDk3MTUyMF0sImh0dHBzIjp0cnVlLCJtZXRob2QiOiJQT1NUIiwibWltZUxpbWl0IjpbImltYWdlLyoiLCJ2aWRlby8qIiwiYXVkaXQvKiJdLCJyZXZpZXciOjAsInRpbWVzdGFtcCI6MTY5MDE5MTYzNywidXJsIjoiaHR0cHM6Ly9mcC5wcy5uZXRlYXNlLmNvbS9sMTAtbWQtY24vZmlsZS9uZXcvIn0= */
      token: string;
      /** @example 1690191637 */
      expires: number;
      /** @example l10-md-cn */
      project: string;
      /** @example https://fp.ps.netease.com/l10-md-cn/file/new/ */
      uploadUrl: string;
    };
    FacePinchWorkItem: ({
      /** @example 42448100244 */
      roleId: number;
      /** @example 2 */
      userId: number;
      /**
       * @description 可用的应用部位名称数组（考虑历史数据限制）
       * @example [
       *   "塑形",
       *   "妆容"
       * ]
       */
      availableApplyParts?: ("塑形" | "妆容" | "全脸")[];
    }) & components["schemas"]["FacePinchWorkAddRet"] & components["schemas"]["FacePinchWorkAdd"] & components["schemas"]["FacePinchWorkStat"] & components["schemas"]["FacePinchUserActionFlag"];
    /** @description 收藏配额使用量 每个角色100个 */
    FacePinchWorkCollectUsage: {
      /**
       * @description 最大允许收藏
       * @example 100
       */
      max: number;
      /**
       * @description 当前收藏量
       * @example 10
       */
      current: number;
    };
    /** @description 设计作品上传配额使用量 每个角色100个 */
    FacePinchWorkUploadUsage: {
      /**
       * @description 最大允许上传作品
       * @example 100
       */
      max: number;
      /**
       * @description 当前作品数量
       * @example 10
       */
      current: number;
    };
    /** @description 被举报角色相关信息 */
    FacePinchWorkExtraForAccuse: {
      accusedRole: {
        /**
         * @description 被举报的捏脸站用户id
         * @example 3022
         */
        userId: number;
        /**
         * @description 被举报的角色id
         * @example 42448100244
         */
        roleId: number;
        /**
         * @description 被举报的角色名
         * @example 冰粉不凉
         */
        roleName: string;
        /**
         * @description 被举报的角色等级
         * @example 30
         */
        roleLevel: number;
        /**
         * @description 被举报的服务器
         * @example 15
         */
        server: number;
        /**
         * @description 被举报的vip登记
         * @example 1
         */
        vip: number;
      };
      /**
       * @description 可用的应用部位名称数组（考虑历史数据限制）
       * @example [
       *   "塑形",
       *   "妆容"
       * ]
       */
      availableApplyParts?: ("塑形" | "妆容" | "全脸")[];
    };
    /** @description 添加捏脸作品 */
    FacePinchWorkAddRetTiny: {
      /** @example 1 */
      id: number;
      /**
       * @description 用于生成分享的二维码的链接的分享id
       * @example EB446F
       */
      shareId: string;
      /** @example 1624808747493 */
      createTime: number;
    };
    FacePinchWorkAddRet: components["schemas"]["FacePinchWorkAddRetTiny"] & components["schemas"]["FacePinchWorkStat"];
    FacePinchShareVideoAdd: {
      /** @example 3 */
      id?: number;
      /**
       * @description 分享视频（游戏内生成的视频链接）
       * @example http://hi-163-qnm.nosdn.127.net/face_pinch/image/ddcc58b01bc811eeb3b69f53439eb902
       */
      video?: string;
      /**
       * @description 分享视频生成的分享id
       * @example uOh7I_nKZmj9w6wHLP5fj
       */
      shareId?: string;
    };
    /** @description 作品相关统计值 */
    FacePinchUserActionFlag: {
      /**
       * @description 是否已收藏
       * @example false
       */
      isLiked: boolean;
      /**
       * @description 是否已收藏
       * @example false
       */
      isCollected: boolean;
      /**
       * @description 是否曾经应用过
       * @example false
       */
      onceApplied: boolean;
    };
    /** @description 作品相关统计值 */
    FacePinchWorkStat: {
      /**
       * @description 总热度
       * @example 0
       */
      hot: number;
      /**
       * @description 被点赞计数
       * @example 0
       */
      likeCount: number;
      /**
       * @description 被收藏计数
       * @example 0
       */
      collectCount: number;
      /**
       * @description 被使用创角的计数
       * @example 0
       */
      applyCount: number;
    };
    /** @description 添加捏脸作品 */
    FacePinchWorkAdd: {
      /**
       * @description 捏脸数据对应的nos连接
       * @example http://hi-163-qnm.nosdn.127.net/face_pinch/ddcc58b01bc811eeb3b69f53439eb902
       */
      dataUrl: string;
      /**
       * @description 作品大图（游戏内直接传来的一张大图）
       * @example http://hi-163-qnm.nosdn.127.net/face_pinch/image/ddcc58b01bc811eeb3b69f53439eb902
       */
      image: string;
      /**
       * @description 分享视频（游戏内生成的视频链接）
       * @example http://hi-163-qnm.nosdn.127.net/face_pinch/image/ddcc58b01bc811eeb3b69f53439eb902
       */
      video?: string;
      /**
       * @description 作品名字
       * @example 作品名称7个字
       */
      title: string;
      /**
       * @description 作品描述
       * @example 作品介绍
       */
      desc?: string;
      /**
       * @description 作者名称, 预创建阶段请使用随机昵称
       * @example 雪花里没雪
       */
      roleName: string;
      /**
       * @description 作品所能应用性别
       * @example 1
       */
      gender: number;
      /**
       * @description 公开级别， 0 => 公开 1 => 私有
       * @example 0
       * @enum {number}
       */
      visibility?: 0 | 1;
      /**
       * @description 作品类型 0 => 标准,  1 => 不出现在作品列表中, 只是为了生成二维码数据
       * @example 0
       * @enum {number}
       */
      workType?: 0 | 1;
      bodyShape?: components["schemas"]["bodyShape"];
      /**
       * @description 作品所能应用职业id
       * @example 2
       */
      jobId: number;
      /**
       * @description 允许使用的部位：1=塑形，2=妆容，4=全脸，可组合使用位运算
       * @default 7
       * @example 7
       */
      allowedParts: number;
    };
    /**
     * @description |key|desc|
     * |--|--|
     * |0|成年男性|
     * |1|成年女性|
     * |2|壮男|
     * |3|萝莉|
     *
     * @example 0
     * @enum {number}
     */
    bodyShape: 0 | 1 | 2 | 3;
    ExpressionBase: {
      /** @example 0 */
      expression?: number;
      /** @example 0 */
      expression_txt?: number;
      /** @example 0 */
      sticker?: number;
      /** @example 0 */
      frame?: number;
    };
    ThisDayThatYearHasMomentData: {
      /**
       * @description 是否发表过
       * @example true
       */
      hasMoment: boolean;
    };
    MomentListHotData: {
      list: components["schemas"]["MomentListItemShow"][];
      /**
       * @description 总数量
       * @example 5
       */
      count: number;
    };
    ThisDayThatYearMomentListData: {
      list: components["schemas"]["MomentListItemShow"][];
      /**
       * @description 总数量
       * @example 5
       */
      count: number;
    };
    MomentListItemShow: {
      /** @example 15938816530 */
      id: number;
      /** @example 100100001 */
      roleid: number;
      /** @example */
      text: string;
      imglist: ({
          /** @example http://hi-163-qnm.nosdn.127.net/moment/202112/22/9a0cfc4062f311ecb24bedc4ad7046b7?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc= */
          pic: string;
          /** @example http://hi-163-qnm.nosdn.127.net/moment/202112/22/9a0cfc4062f311ecb24bedc4ad7046b7?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=%7cimageView&thumbnail=250y160 */
          thumb: string;
        })[];
      /** @example 1640155894489 */
      createtime: number;
      /** @example 0 */
      status: number;
      /** @example false */
      is_user_top: boolean;
      videolist: unknown[];
      /** @example 0 */
      zancount: number;
      /** @example 0 */
      commentcount: number;
      /** @example false */
      is_user_liked: boolean;
      zanlist: unknown[];
      /** @example true */
      commentable: boolean;
      commentlist: unknown[];
      /** @example 0 */
      forwardcount: number;
      /** @example null */
      forwardmoment: Record<string, never>;
      previousforwards: unknown[];
      /** @example false */
      isfollowing: boolean;
      /** @example 琳琅安澜 */
      rolename: string;
      /** @example 1 */
      server_id: number;
      /** @example */
      server_name: string;
      /** @example 68 */
      grade: number;
      /** @example 0 */
      gender: number;
      /** @example 1 */
      clazz: number;
      /** @example 123 */
      expression_base: string;
      /** @example https://hi-163-qnm.nosdn.127.net/photo/202112/27/85295a3066e011eca0c375c49db0fb34 */
      photo: string;
      used_name: unknown[];
      /** @example 0 */
      splevel: number;
      /** @example 0 */
      xianfanstatus: number;
      lianghao: {
        /** @example 0 */
        id: number;
        /** @example 0 */
        expiredtime: number;
        /** @example 0 */
        hideicon: number;
        /** @example 0 */
        showdigit: number;
      };
      /**
       * @description 是否包含抽奖
       * @example false
       */
      has_lottery: boolean;
      /** @description 抽奖信息, 只在包含抽奖的动态返回 */
      lottery?: components["schemas"]["moment-list-item-show-lottery"];
    };
    RecentLikeUser: {
      /** @example 42448100244 */
      roleId: number;
      /** @example 小龍男 */
      roleName: string;
    };
    ReplyComment: {
      /** @example 1143589 */
      id: number;
      /** @example 42448100244 */
      roleId: number;
      /** @example null */
      replyId: string;
      /** @example 3046 */
      cardId: number;
      /** @example 介绍一下这个支线的历史背景，里面段誉也说了，高氏是因为帮助过大理段氏建立基业所以位极人臣 */
      text: string;
      /** @example 1624808747493 */
      createTime: number;
      /** @example 小龍男 */
      roleName: string;
      /** @example 7 */
      clazz: number;
      /** @example 0 */
      gender: number;
    };
    CardLikeItem: {
      /** @example 1034 */
      id: number;
      /** @example 2127 */
      cardId: number;
      /** @example 1608709903443 */
      createTime: number;
    };
    RoleInfoShowTiny: {
      /**
       * @description 评论玩家角色id
       * @example 1487200202
       */
      roleId: number;
      /**
       * @description 角色名
       * @example 凤凰羽
       */
      roleName: string;
      /**
       * @description 职业
       * @example 6
       */
      clazz: number;
      /**
       * @description 等级
       * @example 60
       */
      grade: number;
      /**
       * @description 性别
       * @example 0
       */
      gender: number;
      /**
       * @description 仙凡状态
       * @example 1
       */
      xianfanstatus: number;
    };
    CardNotionItemDetailShow: components["schemas"]["CardNotionItemShow"] & components["schemas"]["CardNotionItemDetailExtra"];
    CardNotionItemDetailExtra: {
      likeUsers: components["schemas"]["RecentLikeUser"][];
      card?: components["schemas"]["CardInfo"];
    };
    CardNotionItemShow: components["schemas"]["CardNotionItem"] & components["schemas"]["RoleInfoShowTiny"];
    CardNotionCommentShow: components["schemas"]["CardNotionCommentItem"] & components["schemas"]["RoleInfoShowTiny"] & components["schemas"]["CardNotionCommentReplayItem"];
    CardNotificationShow: {
      notifier: components["schemas"]["RoleInfoShowTiny"];
    } & components["schemas"]["CardNotificationItem"];
    CardNotionCommentReplayItem: {
      /**
       * @description 回复的评论id
       * @example 10
       */
      replyId: number;
      /**
       * @description 回复的玩家id
       * @example 501034
       */
      replyRoleId: number;
      /**
       * @description 回复的玩家名字
       * @example 玩家名字七个字
       */
      replyRoleName: string;
    };
    CardNotificationItem: {
      /**
       * @description 想法id
       * @example 501034
       */
      id: number;
      /**
       * @description 通知类型 1 => 想法被点赞  2 => 想法被评论  3 => 想法的评论被回复
       * @example 1
       * @enum {number}
       */
      type: 1 | 2 | 3;
      /**
       * @description 通知的文本， 1的时候为空， 如果通知是2， 那就是评论的文本， 如果是3， 那就回复的文本
       * @example 评论消息评论消息
       */
      text: string;
      /** @description 通知类型为1, 2的时候，该字段存在 */
      notion: {
        /**
         * @description 想法id
         * @example 501034
         */
        id?: number;
        /**
         * @description 想法文本
         * @example 讲道理，这段剧情真的很棒
         */
        text?: string;
      };
      /** @description 通知类型为3的时候，该字段存在 */
      notionComment?: {
        /**
         * @description 评论id
         * @example 501034
         */
        id?: number;
        /**
         * @description 评论文本
         * @example emmm, 讲道理这段剧情真的很棒! 场景也美
         */
        text?: string;
      };
      /** @example 1529722981979 */
      createTime: number;
    };
    CardNotionCommentItem: {
      /**
       * @description 评论id
       * @example 501034
       */
      id: number;
      /**
       * @description 卡片id
       * @example 2723
       */
      cardId: number;
      /**
       * @description 想法id
       * @example 2723
       */
      notionId: number;
      /**
       * @description 评论文本
       * @example 物是人非事事休，欲语泪先流。。。。我本意助其三人快意洒脱笑余生
       */
      text: string;
      /** @example 1529722981979 */
      createTime: number;
    };
    CardNotionItem: {
      /**
       * @description 想法id
       * @example 501034
       */
      id: number;
      /**
       * @description 卡片id
       * @example 2723
       */
      cardId: number;
      /**
       * @description 评论文本
       * @example 物是人非事事休，欲语泪先流。。。。我本意助其三人快意洒脱笑余生
       */
      text: string;
      /**
       * @description 评论热度
       * @example 587611
       */
      hot: number;
      /** @example 1529722981979 */
      createTime: number;
      /**
       * @description 点赞数
       * @example 58675
       */
      likeCount: number;
      /**
       * @description 二级评论数
       * @example 1000
       */
      commentCount: number;
      /**
       * @description 是否点赞过
       * @example false
       */
      isLiked: boolean;
    };
    ApiOkRes: {
      /**
       * @description 返回值,0正常, 其他皆为异常
       * @example 0
       */
      code?: number;
      /** @description 具体返回的数据对象 */
      data?: Record<string, never>;
    };
    ApiArrOkRes: {
      /**
       * @description 返回值,0正常, 其他皆为异常
       * @example 0
       */
      code?: number;
      /** @description 具体返回的数据对象 */
      data?: unknown[];
    };
    CardInfo: {
      /**
       * @description 卡片id
       * @example 10
       */
      cardId: number;
      /**
       * @description 点赞数
       * @example 10
       */
      likeCount: number;
      /**
       * @description 是否点赞
       * @example false
       */
      isLiked: boolean;
    };
    /**
     * @description | id  | desc           |
     * | --- | ------         |
     * | 0   | 无任何暗恋状态 |
     * | 1   | 暗恋中         |
     * | 2   | 互相暗恋       |
     *
     * @example 1
     * @enum {number}
     */
    ESlientLoveStatus: 0 | 1 | 2;
    EquipListItem: {
      /** @example 1 */
      Rank: number;
      /** @example 600002 */
      RoleId: number;
      /** @example 2 */
      EquipId: string;
      /** @example http://hi-163-qnm.nosdn.127.net/asserts/icons/128x128/equip/23000000.jpg */
      EquipIcon: string;
      /** @example 轻罗扇 */
      EquipName: string;
      /** @example 100 */
      Score: number;
      /** @example 1 */
      PositionId: number;
      /** @example 繁花诱惑 */
      RoleName: string;
      /** @example http://hi-163-qnm.nosdn.127.net/avatars/job_7_female.jpg */
      Avatar: string;
      EquipDetail: {
        /** @example http://res.qnm.netease.com/xt/icon/Item_Equipment/Wuqi/Icon/wp_031_01-b.jpg */
        small: string;
        /** @example <img src="http://res.qnm.netease.com/xt/icon/Item_Equipment/Wuqi/Icon/wp_031_01-b.jpg" /><br/><span style="color:#519FFF">轻罗扇</span><br/><span>琴扇</span><span class="fr">1</span><br/>装备评分 110<br/><b class="entry">基础属性</b><br/> <span style="color:#FFFFFF">物理攻击 27-72</span><span style="color:#00FFFF"></span><br/> <span style="color:#FFFFFF">法术攻击 36-87</span><span style="color:#00FFFF"></span><br/> <span style="color:#FFFFFF">攻击速度 1.237</span><br/><b class="entry">升级完美度</b> <br/><b class="entry">耐久度</b> 80/80<br/><b class="entry">词条属性</b><br/> <span style="color:#519FFF">【坚脆】不可修复（基础属性+50%）</span><br/> <span style="color:#519FFF">【透微】法术命中 +2</span><br/> <span style="color:#519FFF">【惊帆】敏捷 +2</span><br/> */
        desc: string;
        /** @example blue */
        color: string;
      };
    };
    EquipWeaponsRankData: {
      list: components["schemas"]["EquipListItem"][];
      /** @example 2018-05-09 13:09:44 */
      updateTime: string;
      pagination: {
        /** @example 1 */
        cur: number;
        /** @example 1 */
        total: number;
      };
    };
    ComplainAddData: {
      /**
       * @description 举报记录id
       * @example b037761c50e711ecbf630242ac130002
       */
      id: string;
    };
    EquipCommentListData: {
      list: string[];
    };
    WeaponFilterData: {
      /**
       * @description key为游戏内服务器大区名字
       * @example {"盘古谣": [{ "id": 20, "name": "仙剑问情" }, { "id": 21, "name": "兰若开天" }]}
       */
      servers: {
        [key: string]: {
          /** @example 20 */
          id: number;
          /** @example 仙剑问情 */
          name: string;
        };
      };
      equipPositions: {
          /** @example 1 */
          id: number;
          /** @example 刀剑 */
          name: string;
        }[];
    };
    SlientLoveAdd: {
      /**
       * @description 新增关系记录id
       * @example 1
       */
      id: number;
      status: components["schemas"]["ESlientLoveStatus"];
    };
    CardNotionAdd: {
      /**
       * @description 新增卡片想法id
       * @example 1
       */
      id: number;
    };
    CommonLikeAction: {
      /**
       * @description 点赞id
       * @example 1
       */
      id: number;
    };
    CardNotionLikeAction: {
      /**
       * @description 卡片想法点赞id
       * @example 1
       */
      id: number;
    };
    getProfile: {
      /** @example 0 */
      code: number;
      data: {
        /** @example 100100001 */
        roleid: number;
        /** @example 大师傅 */
        location: string;
        /** @example 近近景近景123123123 */
        signature: string;
        signaturevoice: {
          /** @example */
          time: string;
          /** @example */
          url: string;
        };
        /** @example 1 */
        showphoto: number;
        /** @example 1 */
        photoaudit: number;
        privacy: {
          /** @example false */
          location: boolean;
          /** @example true */
          space: boolean;
          /** @example false */
          name: boolean;
          /** @example true */
          msg_for_following: boolean;
          /** @example true */
          hide_lbs: boolean;
        };
        /** @example 100200001,100400001 */
        friendlist: string;
        /** @example 15060 */
        renqi: number;
        /** @example 2000 */
        gift: number;
        /** @example 73 */
        flower: number;
        /** @example 15059 */
        flowerrenqi: number;
        /** @example 30758 */
        sendflowerrenqi: number;
        wedding: {
          /** @example 0 */
          weddingtime: number;
          /** @example 0 */
          weddingindex: number;
          partner: {
            /** @example 0 */
            id: number;
            /** @example 0 */
            xianfanstatus: number;
          };
          self: {
            /** @example 100100001 */
            roleid: number;
            /** @example 100100001 */
            rolename: string;
            /** @example 1 */
            server_id: number;
            /** @example 0 */
            gender: number;
            /** @example 160 */
            grade: number;
            /** @example 7 */
            clazz: number;
            /** @example 0 */
            titleid: number;
            /** @example 0 */
            gangid: string;
            /** @example */
            gang: string;
            /**
             * @example [
             *   "幽灵随心"
             * ]
             */
            used_name: string[];
            /** @example 0,611526;1,95106;2,99257;3,62324;4,11812;5,291200;6,0;7,0;8,51827;9,0;10,0 */
            fightingcapacity: string;
            /** @example 0 */
            usednamestatus: number;
            /** @example 1 */
            xianfanstatus: number;
            /** @example */
            server_name: string;
            /** @example 100100001 */
            id: number;
            /** @example 100100001 */
            name: string;
          };
        };
        /** @example 1616055899766 */
        updatetime: number;
        expression_base: {
          /** @example 0 */
          expression: number;
          /** @example 0 */
          expression_txt: number;
          /** @example 0 */
          sticker: number;
          /** @example 0 */
          frame: number;
        };
        expression_extra: {
          /** @example 0 */
          expression_txt_offset_x: number;
          /** @example 0 */
          expression_txt_offset_y: number;
          /** @example 100 */
          expression_txt_scale_x: number;
          /** @example 100 */
          expression_txt_scale_y: number;
          /** @example 0 */
          expression_txt_rotation: number;
          /** @example 0 */
          sticker_offset_x: number;
          /** @example 0 */
          sticker_offset_y: number;
          /** @example 78 */
          sticker_scale_x: number;
          /** @example 78 */
          sticker_scale_y: number;
          /** @example 0 */
          sticker_rotation: number;
        };
        /** @example ************** */
        lastloginip: string;
        lianghao: {
          /** @example 666666 */
          id: number;
          /** @example 1577761481 */
          expiredtime: number;
          /** @example 0 */
          hideicon: number;
          /** @example 0 */
          showdigit: number;
        };
        /** @example 100100001 */
        rolename: string;
        /** @example 1 */
        server_id: number;
        /** @example 0 */
        gender: number;
        /** @example 160 */
        grade: number;
        /** @example 7 */
        clazz: number;
        /** @example 0 */
        titleid: number;
        title: {
          /** @example 0 */
          id: number;
        };
        /** @example 0 */
        gangid: string;
        /** @example */
        gang: string;
        /**
         * @example [
         *   "幽灵随心"
         * ]
         */
        used_name: string[];
        /** @example 0,611526;1,95106;2,99257;3,62324;4,11812;5,291200;6,0;7,0;8,51827;9,0;10,0 */
        fightingcapacity: string;
        /** @example 0 */
        usednamestatus: number;
        /** @example 1 */
        xianfanstatus: number;
        /** @example */
        server_name: string;
        guild: {
          /** @example 0 */
          id: string;
          /** @example */
          name: string;
        };
        /** @example false */
        hideusedname: boolean;
        /** @example false */
        isfollowing: boolean;
        slientLove: components["schemas"]["ESlientLoveStatus"];
        background: {
          /** @example 2 */
          backgroundid: number;
          /** @example 1616061085596122 */
          backgroundvalidity: number;
        };
      };
    };
    syncBackgroundReq: {
      /** @example 100100001 */
      roleid: number;
      /**
       * @description 背景图片id 为1到9
       * @example 1
       */
      id: number;
      /**
       * @description 背景有效时间点 时间戳
       * @example 1616061085596
       */
      validity: number;
    };
    syncBackgroundRes: {
      /** @example 0 */
      code: number;
      data: {
        /** @example 1 */
        affectedRows: number;
      };
    };
    /** @description 分页结构元信息 */
    PaginationMeta: {
      totalPage?: number;
      curPage?: number;
      totalCount?: number;
    };
    ReportLogItem: {
      /** @example 1 */
      id: number;
      /**
       * @description 设备id
       * @example device_id
       */
      deviceId: string;
      /** @example http://device_example.log */
      url: string;
      /** @example 1617179813989 */
      createTime: number;
    };
    FireworkPhotoListRes: {
      /** @example 0 */
      code: number;
      data: {
        list: {
            /** @example 1 */
            id: number;
            /** @example 1 */
            index: number;
            /** @example 100100001 */
            roleId: number;
            /** @example http://hi-163-qnm.nosdn.127.net/fireworks/202011/09/3ec1a350223911eb9d40d59fffee58e4.jpg */
            url: string;
            /** @example 0 */
            auditStatus: number;
          }[];
        /** @example 2 */
        count: number;
      };
    };
    NosToken: {
      /** @example UPLOAD 80daf02d6f7042b190ef3358091cc335:Vywi0Ft15554tdPO35a9ew23PdWJ4c8Fmkx4beZJaWw=:eyJCdWNrZXQiOiJoaS0xNjMtcW5tIiwiT2JqZWN0IjoicGhvdG8vMjAyMTAyLzIzLzBkMzEwYTcwNzU4NTExZWI4NTAwNTcxNTU3NDBkOTIyLmpwZyIsIkV4cGlyZXMiOjE2MTQwNTA1ODcsIlJldHVybkJvZHkiOiJ7XCJjb2RlXCI6MCxcImRhdGFcIjp7XCJidWNrZXRuYW1lXCI6XCIkKEJ1Y2tldClcIixcInVybFwiOlwiaHR0cDovLyQoQnVja2V0KS5ub3Nkbi4xMjcubmV0LyQoT2JqZWN0KVwifX0iLCJDYWxsYmFja1VybCI6IiIsIkNhbGxiYWNrQm9keSI6IiJ9 */
      token: string;
      /** @example hi-163-qnm */
      bucketname: string;
      /** @example photo/202102/23/0d310a70758511eb850057155740d922.jpg */
      objectname: string;
      /** @example 1614050587 */
      expires: number;
      /** @example http://hi-163-qnm.nosdn.127.net/ */
      prefix: string;
    };
    GetLocationData: {
      /** @example 中国 */
      country: string;
      /** @example 浙江 */
      province: string;
      /** @example 杭州 */
      city: string;
    };
    FacePinchWebPublicShareInfo: {
      /** @example uOh7I_nKZmj9w6wHLP5fj */
      shareId?: string;
      /**
       * @description 捏脸站游戏内上传截图(aka 分享页的封面)
       * @example https://l10-md-cn.fp.ps.netease.com/file/64dc945202af4626152bf478i9JjHSy205
       */
      image?: string;
    };
    FacePinchWebPublicShareVideoInfo: {
      /** @example uOh7I_nKZmj9w6wHLP5fj */
      shareId?: string;
      /**
       * @description 捏脸视频url
       * @example http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4
       */
      video?: string;
    };
    AddWishPayload: {
      /** @example 100100001 */
      roleid?: number;
      /** @example 9c3dbc74f33dd3f2 */
      wishId?: string;
      /**
       * @description | value | desc         |
       * |-------|--------------|
       * | 0     | 文本类型心愿 |
       * | 1     | 物品类型心愿 |
       *
       * @example 1
       * @enum {number}
       */
      type?: 0 | 1;
      /** @example example text */
      text?: string;
      /** @example 111 */
      templateId?: number;
      /** @example 1 */
      regionId?: number;
      /** @example 11 */
      num?: number;
      /** @example 11 */
      totalProgress?: number;
      /** @example 1599725110252 */
      startTime?: number;
      /** @example 1599725110252 */
      endTime?: number;
      /**
       * @description | value | desc |
       * | --    | --   |
       * | 0     | 默认类型分享动态   |
       * | 1     | 年终总结分享动态的类型   |
       */
      shareType?: number;
      /** @description 心愿分享生成的动态的图片列表 */
      shareImgs?: string[];
    };
    /**
     * @description | status | description |
     * | ---- | -- |
     * | 0 | 审核中 |
     * | 1 | 审核通过 |
     * | -1 | 审核拒绝 |
     *
     * @enum {number}
     */
    AuditStatus: 0 | 1 | -1;
    /**
     * @description | sex | value |
     * | ---- | -- |
     * | male | 0 |
     * | female | 1 |
     *
     * @enum {integer}
     */
    Gender: 0 | 1;
    /**
     * @description | id | name |
     * |------|--------|
     * | 1  | 射手   |
     * | 2  | 甲士   |
     * | 3  | 刀客   |
     * | 4  | 侠客   |
     * | 5  | 方士   |
     * | 6  | 医师   |
     * | 7  | 魅者   |
     * | 8  | 异人   |
     * | 9  | 偃师   |
     * | 11  | 影灵   |
     * | 12  | 蝶客  |
     *
     * @example 12
     * @enum {integer}
     */
    Clazz: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 11 | 12;
    /**
     * @description 由gender和jobId可生成  ```https://hi-163-qnm.nosdn.127.net/avatars/job_${clazz}_${sex}.jpg```
     *
     * @example https://hi-163-qnm.nosdn.127.net/avatars/job_1_male.jpg
     */
    JobAvatar: string;
    Topic: {
      ID?: number;
      Name?: string;
      Banner?: string;
      Url?: string;
      Desc?: string;
      CreateTime?: number;
    };
    Article: {
      title?: string;
      content?: string;
    };
    ReachFollowLimitError: {
      code?: number;
      msg?: string;
    };
    SuccRes: {
      code?: number;
    };
    ApiAction: {
      /**
       * @description 操作是否正常
       * @example true
       */
      isOk: boolean;
    };
    /** @example null */
    ApiOkData: Record<string, never>;
    CommonAdd: {
      /**
       * @description 添加生成的id
       * @example 10
       */
      id: number;
    };
    TagFilterSchema: {
      /**
       * @description 是否选择所有tag, 默认为true
       * @default true
       */
      selectAllTags?: boolean;
      /** @description 只在selectAllTags=false的时候生效, 标签id列表, 0代表选中其他标签 */
      tagIds?: number[];
    };
    MomentAllServerHotListRequestBody: ({
      /** @example 100100001 */
      roleid?: number;
      /**
       * @description 指返回里的hot_index, 从1开始计数
       * @example 10
       */
      lastid?: number;
      /**
       * @description 文本风格
       * @default plain
       * @enum {string}
       */
      textStyle?: "plain" | "html";
    }) & components["schemas"]["TagFilterSchema"];
    MomentHotListRequestBody: components["schemas"]["MomentAllServerHotListRequestBody"] & {
      /** @example 1 */
      serverid?: number;
    };
    MomentListByGuildReq: ({
      /** @example 100100001 */
      roleid?: number;
      /** @example 100100001 */
      guildid?: number;
      /** @description 返回比该id小的数据 */
      lastid?: number;
      /**
       * @description 文本风格
       * @default plain
       * @enum {string}
       */
      textStyle?: "plain" | "html";
    }) & components["schemas"]["TagFilterSchema"];
    MomentListRequestBody: {
      /** @example 100100001 */
      roleid?: number;
      /** @example 100100001 */
      targetid?: number;
      /** @description 返回比该id小的数据 */
      lastid?: number;
      /**
       * @description 文本风格
       * @default plain
       * @enum {string}
       */
      textStyle?: "plain" | "html";
      /**
       * @description 是否选择所有tag, 默认为true
       * @default true
       */
      selectAllTags?: boolean;
      /** @description 只在selectAllTags=false的时候生效, 标签id列表, 0代表选中其他标签 */
      tagIds?: number[];
    };
    /** @description 标签id列表 */
    MomentAddTagIds: number[];
    MomentAddRequestBody: {
      /** @example 100100001 */
      roleid?: number;
      /** @example 我是文本 */
      text?: string;
      /** @example ["http://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d"] */
      imglist?: string;
      /** @example ["http://hi-163-qnm.nosdn.127.net/upload/201905/13/deaab8a0754e11e9bbd4a9db24757383.mp4"] */
      videolist?: string;
      tagIds?: components["schemas"]["MomentAddTagIds"];
    };
    "music-club-create-req": {
      /**
       * Format: int64
       * @description 乐团ID
       * @example 1
       */
      musicClubId: number;
      /**
       * @description 乐团名称
       * @example 梦幻交响乐团
       */
      name: string;
      /**
       * Format: int64
       * @description 服务器ID
       * @example 548
       */
      serverId: number;
      /**
       * Format: int64
       * @description 经理角色ID
       * @example 281851200548
       */
      managerRoleId: number;
    };
    "music-club-create-resp": {
      /**
       * Format: int32
       * @description 状态码
       * @example 0
       */
      code?: number;
      data?: {
        /**
         * Format: int64
         * @description 乐团ID
         * @example 1
         */
        musicClubId: number;
        /**
         * @description 是否覆盖创建, true-覆盖创建, false-正常创建
         * @example false
         */
        isOverwrite: boolean;
      };
    };
    "music-club-disband-resp": {
      /**
       * Format: int32
       * @description 状态码
       * @example 0
       */
      code?: number;
      data?: {
        /**
         * Format: int64
         * @description 乐团ID
         * @example 1
         */
        musicClubId: number;
        /**
         * Format: int64
         * @description 解散时间戳
         * @example 1718000000000
         */
        disbandTime: number;
      };
    };
    /** @description 乐团主打唱片 */
    "music-club-recording-lead-single": {
      /**
       * @description 唱片id
       * @example 1
       */
      id: number;
      /**
       * @description 唱片名称
       * @example 唱片名字
       */
      name: string;
      /**
       * @description 唱片热度
       * @example 100
       */
      hot: number;
    };
    "music-club-show-resp": {
      /**
       * Format: int64
       * @description 乐团ID
       * @example 1
       */
      musicClubId: number;
      /**
       * @description 乐团名称
       * @example 梦幻交响乐团
       */
      name: string;
      /**
       * Format: int64
       * @description 500名内显示具体名次，超出500名输出0
       * @example 1
       */
      rank: number;
      /**
       * Format: int64
       * @description 在架唱片数量
       * @example 10
       */
      recordingNum: number;
      leadingRecording: components["schemas"]["music-club-recording-lead-single"];
    };
    "music-club-update-req": {
      /**
       * Format: int64
       * @description 乐团ID
       * @example 1
       */
      musicClubId: number;
      /**
       * @description 乐团名称
       * @example 梦幻交响乐团
       */
      name?: string;
      /**
       * @description 新等级
       * @example 1
       */
      level?: number;
      /**
       * Format: int64
       * @description 新经理角色ID
       * @example 11
       */
      managerRoleId?: number;
    };
    "music-club-update-resp": {
      /**
       * Format: int32
       * @description 状态码
       * @example 0
       */
      code?: number;
      data?: {
        /**
         * Format: int64
         * @description 乐团ID
         * @example 1
         */
        musicClubId: number;
        /**
         * Format: int64
         * @description 更新时间戳
         * @example 1718000000000
         */
        updateTime: number;
      };
    };
    "music-club-rank-item": {
      /**
       * @description 排名
       * @example 1
       */
      rank: number;
      /** @description 乐团ID */
      id: number;
      /**
       * @description 服务器ID
       * @example 1
       */
      serverId: number;
      /**
       * @description 乐团名称
       * @example 乐团名字
       */
      name: string;
      /**
       * @description 乐团等级
       * @example 1
       */
      grade: number;
      leadSingleRecording: components["schemas"]["music-club-recording-lead-single"];
      /**
       * @description 乐团热度
       * @example 100
       */
      hot: number;
    };
    /** @description 唱片上架请求所需信息 */
    "music-club-recording-release-req": {
      /**
       * @description 唱片录制唯一id, 乐队演出后在本地录制后在本地就能生成, 游戏方生成, 用于唯一标识一个唱片录制
       * @example 1234567890abcdef
       */
      trackId: string;
      /**
       * @description 唱片名称
       * @example 唱片名字
       */
      name: string;
      /**
       * @description 副歌开始位置，单位秒
       * @example 10
       */
      chorusStart: number;
      /**
       * @description 人声偏移时间，单位毫秒, 正数表示人声提前，负数表示人声延后
       * @example 10
       */
      vocalOffset: number;
      /**
       * @description 人声音量比列
       * @example 70
       */
      vocalVolume: number;
      /**
       * @description 服务器id
       * @example 1
       */
      serverId: number;
      /**
       * @description 乐器声音量比列
       * @example 70
       */
      instrumentVolume: number;
      /**
       * @description 唱片数据url
       * @example http://fp.netease.com/music_club/recording/my_recording.data
       */
      dataUrl: string;
      /**
       * @description 唱片所属乐团id
       * @example 1
       */
      musicClubId: number;
      /**
       * @description 人声音频文件URL，用于人声审核
       * @example http://fp.netease.com/music_club/recording/my_vocal.mp3
       */
      vocalUrl: string;
      /**
       * @description 唱片时长，单位毫秒
       * @example 180000
       */
      duration: number;
    };
    /** @description 唱片上架返回信息 */
    "music-club-recording-release-resp": {
      /**
       * @description 唱片发行id, 只有唱片上架后才有
       * @example 1
       */
      id: number;
      /**
       * @description 发行时间, 是指唱片上架的时间, 单位ms
       * @example 1716835200000
       */
      releaseTime: number;
    };
    "music-club-rank-tiny": {
      /** @description 乐团ID */
      id: number;
      /**
       * @description 服务器ID
       * @example 1
       */
      serverId: number;
      /**
       * @description 乐团名称
       * @example 乐团名字
       */
      name: string;
    };
    /** @description 点播界面的乐团唱片展示信息 */
    "music-club-recording-show": components["schemas"]["music-club-recording-release-req"] & components["schemas"]["music-club-recording-release-resp"] & {
      musicClub: components["schemas"]["music-club-rank-tiny"];
      /**
       * @description 点播次数
       * @example 100
       */
      requestPlayCount: number;
      /**
       * @description 评分
       * @example 9.8
       */
      rating: number;
      /**
       * @description 我的评分
       * @example 4.5
       */
      myRating: number;
      /**
       * @description 当前用户是否已评分
       * @example true
       */
      isRated: boolean;
    };
    /** @description 同步点播操作行为请求所需信息 */
    "music-club-radio-request-play-req": {
      /**
       * @description 发起点播角色ID
       * @example 1
       */
      fromRoleId: number;
      /**
       * @description 接收点播角色ID
       * @example 1
       */
      toRoleId: number;
      /**
       * @description 唱片发行id
       * @example 1
       */
      recordingId: number;
      /**
       * @description 事件时间戳, 单位ms
       * @example 1716888888000
       */
      eventTime: number;
    };
    /** @description 同步点播操作行为请求所需信息 */
    "music-club-radio-request-play-resp": {
      /**
       * @description 唱片发行id
       * @example 1
       */
      recordingId: number;
    };
    "music-club-recording-audit-callback-req": {
      /**
       * @description 人声音频文件URL
       * @example https://example.com/vocal/abc123.mp3
       */
      vocalUrl: string;
      /**
       * @description 审核状态，1表示通过，-1表示拒绝
       * @example 1
       * @enum {integer}
       */
      auditStatus: 1 | -1;
      /**
       * @description 审核拒绝原因，当auditStatus为-1时使用
       * @example 包含不当内容
       */
      rejectReason?: string;
    };
    "music-club-recording-audit-callback-resp": {
      /**
       * Format: int32
       * @description 状态码
       * @example 0
       */
      code?: number;
      data?: {
        /**
         * @description 处理是否成功
         * @example true
         */
        success?: boolean;
      };
    };
    /** @description 唱片下架返回信息 */
    "music-club-recording-remove-resp": {
      /**
       * @description 唱片发行id, 只有唱片上架后才有
       * @example 1
       */
      id: number;
      /**
       * @description 指唱片下架的时间, 单位ms
       * @example 1716835200000
       */
      removeTime: number;
    };
    /** @description 唱片打分请求所需信息 */
    "music-club-recording-rate-req": {
      /**
       * @description 打分 1-10分
       * @example 1
       */
      rating: number;
      /**
       * @description 唱片发行id
       * @example 1
       */
      recordingId: number;
    };
    /** @description 唱片打分返回信息 */
    "music-club-recording-rate-resp": {
      /**
       * @description 打分记录id
       * @example 1
       */
      id: number;
      /**
       * @description 打分时间, 单位ms
       * @example 1716835200000
       */
      ratingTime: number;
    };
    /** @description 玩家和当前抽奖动态的相关关系 */
    "moment-list-item-show-lottery": {
      /**
       * @description 抽奖状态 0 未开奖 1 已开奖 2 已取消
       * @example 1
       */
      drawStatus: number;
      /**
       * @description 参与状态 0 未参与 1 已参与
       * @example 是否参与
       */
      isParticipate: boolean;
      /**
       * @description 是否是自己发起的抽奖
       * @example true
       */
      isMyLottery: boolean;
      /**
       * @description 当前玩家是否已经中奖
       * @example true
       */
      isWin: boolean;
    };
    /** @description 角色登录梦岛返回数据 */
    "auth-login-resp": {
      /**
       * @description 会话密钥(Session Key)
       * @example WyIxNzQ5NzIzMTU1MDAwIiwicm9sZSIsIjUyMDIwMjQ4NSIsIlFOTW9iaWxlMTUyNzExNzE1Il0.0eHIaf96QhqSC5G_Vm3o4DL0TDeUhVXNkFJRdNWMgZ8
       */
      skey?: string;
      /**
       * @description 角色ID
       * @example 520202485
       */
      roleid?: number;
      /**
       * @description 人气值
       * @example 0
       */
      renqi?: number;
      /**
       * @description 鲜花数量
       * @example 0
       */
      flower?: number;
      /**
       * @description 鲜花人气值
       * @example 0
       */
      flowerrenqi?: number;
      /**
       * @description 赠送鲜花人气值
       * @example 0
       */
      sendflowerrenqi?: number;
      /**
       * @description 礼物数量
       * @example 0
       */
      gift?: number;
      /**
       * @description 用户照片URL(可为空)
       * @example null
       */
      photo?: string | null;
      /**
       * @description 是否有照片(0-无 1-有)
       * @example 0
       */
      hasphoto?: number;
      /**
       * @description 本周人气值
       * @example 0
       */
      renqi_weekly?: number;
      /**
       * @description 本周鲜花数量
       * @example 0
       */
      flower_weekly?: number;
      /**
       * @description 本周鲜花人气值
       * @example 0
       */
      flowerrenqi_weekly?: number;
      /**
       * @description 本周赠送鲜花人气值
       * @example 0
       */
      sendflowerrenqi_weekly?: number;
      /**
       * @description 本周礼物数量
       * @example 0
       */
      gift_weekly?: number;
    };
    inform: {
      /** @example 43970 */
      id?: number;
      /** @example 100100001 */
      roleid?: number;
      /** @example 100100001 */
      targetid?: number;
      /** @example 154705111 */
      objectid?: number;
      /** @example */
      relateid?: string | null;
      /** @example 开奖了，看看是谁中奖了? */
      text?: string | null;
      /**
       * @description | type  | en                     | cn               |
       * |-------|-----------------------|------------------|
       * | 0     | Like                   | 点赞             |
       * | 1     | Comment                | 评论             |
       * | 2     | Replay                 | 回复             |
       * | 3     | Forward                | 转发             |
       * | 4     | AtInMoment             | 在动态中@提及    |
       * | 5     | AtInComment            | 在评论中@提及    |
       * | 6     | MomentLotteryDraw      | 动态抽奖开奖     |
       * | 7     | MomentLotteryCancel    | 动态抽奖取消开奖 |
       *
       * @example 6
       */
      type?: number;
      /** @example 1 */
      status?: number;
      /** @example 1739453138319 */
      createtime?: number;
      /** @example https://hi-163-qnm.nosdn.127.net/photo/202412/25/f96ed6b0c28e11ef8a090b4b50ca6217?watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc= */
      photo?: string | null;
      /** @example 1 */
      showphoto?: number;
      /** @example 0 */
      photoaudit?: number;
      /** @example 100200001,100300001,100400001,101100001,101200001 */
      friendlist?: string;
      privacy?: string | null;
      /** @example {"expression":0,"expression_txt":0,"sticker":0,"frame":47,"portraitid":7} */
      expression_base?: string;
      /** @example 0 */
      splevel?: number;
      lianghao?: {
        /** @example 0 */
        id?: number;
        /** @example 0 */
        expiredtime?: number;
        /** @example 0 */
        hideicon?: number;
        /** @example 0 */
        showdigit?: number;
      };
      /** @example 星樱桃 */
      rolename?: string;
      /** @example 1 */
      gender?: number;
      /** @example 150 */
      grade?: number;
      /** @example 8 */
      clazz?: number;
      /** @example 1 */
      server_id?: number;
      /** @example */
      used_name?: string;
      /** @example 0 */
      xianfanstatus?: number;
      /** @example */
      server_name?: string;
      /** @example */
      servername?: string;
      objectinfo?: {
        /** @example 154705111 */
        id?: number;
        /** @example 100100001 */
        roleid?: number;
        /** @example 我是文本 */
        text?: string;
        imglist?: ({
            /** @example https://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d */
            pic?: string;
            /** @example https://hi-163-qnm.nosdn.127.net/moment/201609/01/67f70920700111e6a36399e1934d111d?imageView&thumbnail=250y160 */
            thumb?: string;
          })[];
        /** @example 1 */
        imgaudit?: string | null;
        /** @example */
        videolist?: string | null;
      };
    };
    /** @description 中奖参与资格要求, 至少选择一个 */
    "moment-lottery-requirements": {
      /** @description 参与抽奖需要点赞 */
      like: boolean;
      /** @description 参与抽奖需要评论 */
      comment: boolean;
      /** @description 参与抽奖需要转发 */
      forward: boolean;
      /** @description 参与抽奖需要关注 */
      follow: boolean;
    };
    "moment-lottery-show": components["schemas"]["moment-list-item-show-lottery"] & ({
      /** @example 15938804987 */
      momentId?: number;
      /**
       * @description 抽奖类型 1:阳光普照 2:天选之人
       * @example 1
       */
      type: number;
      /**
       * @description 中奖人数
       * @example 100
       */
      winnerNum: number;
      /**
       * @description 最低参与等级
       * @example 0
       */
      minLevel: number;
      /**
       * @description 当前参与人数
       * @example 999
       */
      participateNum: number;
      /** @description 中奖后可获得的礼品 */
      winPrizes: {
          /**
           * @description 奖品id
           * @example 21000039
           */
          id: number;
          /**
           * @description 奖品的数量
           * @example 5
           */
          num: number;
        }[];
      /**
       * @description 中奖玩家的角色名, 多个中奖者的时候显示中奖者中参与最早的
       * @example 玩家名字七个字
       */
      winnerRoleName?: string;
      /** @description 开奖时间(ms) */
      drawTime: number;
      requirements: components["schemas"]["moment-lottery-requirements"];
      /**
       * @description 参与抽奖范围, 全服或者本服
       * @enum {string}
       */
      serverScope: "all" | "local";
      /** @description 发起者信息 */
      hostPlayer: {
        /** @description 发起者角色id */
        roleId?: number;
        /** @description 服务器id */
        serverId?: number;
        /** @description 发起者角色名 */
        roleName?: string;
        /** @description 服务器名 */
        serverName?: string;
      };
      /** @description 计算抽奖要求中我当前满足的操作 */
      myAction: {
        /** @description 是否已经点赞过 */
        isLike?: boolean;
        /** @description 是否已经评论过 */
        isComment?: boolean;
        /** @description 是否已经转发过 */
        isForward?: boolean;
        /** @description 是否已经关注过 */
        isFollow?: boolean;
      };
    });
  };
  responses: {
    /** @description OK */
    MomentGetDetailRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MomentListItemShow"];
        };
      };
    };
    /** @description OK */
    MomentGetMomentsRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list?: components["schemas"]["MomentListItemShow"][];
          };
        };
      };
    };
    /** @description OK */
    DailyLoginLoginTimeRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["DailyLoginLoginTime"];
        };
      };
    };
    /** @description OK */
    MomentLotteryWinnersRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MomentLotteryWinnersData"];
        };
      };
    };
    /** @description OK */
    ServerMomentLotteryAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["ServerMomentLotteryAddResp"];
        };
      };
    };
    /** @description OK */
    ServerTransferAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["ServerTransferAddData"];
        };
      };
    };
    /** @description OK */
    ThisDayThatYearHasMomentRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["ThisDayThatYearHasMomentData"];
        };
      };
    };
    /** @description OK */
    ThisDayThatYearHasMomentListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["ThisDayThatYearMomentListData"];
        };
      };
    };
    /** @description OK */
    MomentListHotRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["MomentListHotData"];
        };
      };
    };
    /** @description OK */
    CommonLikeActionRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CommonLikeAction"];
        };
      };
    };
    /** @description OK */
    CardNotionLikeActionRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CardNotionLikeAction"];
        };
      };
    };
    /** @description OK */
    FacePinchWorkAddRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchWorkAddRet"];
        };
      };
    };
    /** @description OK */
    FacePinchShareVideoAddRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchShareVideoAdd"];
        };
      };
    };
    /** @description OK */
    FacePinchWorkListSelfRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: {
            list: components["schemas"]["FacePinchWorkItem"][];
            usage: components["schemas"]["FacePinchWorkUploadUsage"];
          };
        };
      };
    };
    /** @description OK */
    FacePinchCloudGameAuthLoginRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: {
            /** @example CHEAT_SKEY_ONLY_FOR_TEST */
            skey: string;
          };
        };
      };
    };
    /** @description OK */
    MomentTagsRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["MomentTagList"];
        };
      };
    };
    /** @description OK */
    AtPlayersListRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["AtPlayersList"];
        };
      };
    };
    /** @description OK */
    FashionLotteryListRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FashionLotteryList"];
        };
      };
    };
    /** @description OK */
    FacePinchWebPublicShareInfoRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchWebPublicShareInfo"];
        };
      };
    };
    /** @description OK */
    FacePinchWebPublicShareVideoInfoRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchWebPublicShareVideoInfo"];
        };
      };
    };
    /** @description OK */
    FacePinchWorkListRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: {
            list: components["schemas"]["FacePinchWorkItem"][];
          };
        };
      };
    };
    /** @description OK */
    FacePinchWorkListCollectRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: {
            list?: components["schemas"]["FacePinchWorkItem"][];
            usage?: components["schemas"]["FacePinchWorkCollectUsage"];
          };
        };
      };
    };
    /** @description OK */
    FacePinchGetFpTokenRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchGetFpToken"];
        };
      };
    };
    /** @description OK */
    FacePinchWorkDetailRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchWorkItem"];
        };
      };
    };
    /** @description OK */
    FacePinchWorkUpdateVisibilityRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["ApiAction"];
        };
      };
    };
    /** @description OK */
    FacePinchWorkGetByShareIdRes: {
      content: {
        "application/json": {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          data?: components["schemas"]["FacePinchWorkAddRetTiny"] & components["schemas"]["FacePinchWorkAdd"] & components["schemas"]["FacePinchWorkExtraForAccuse"];
        };
      };
    };
    /** @description OK */
    ActivityLoginValidateAccessTokenRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /** @example 100100001 */
            roleId?: string;
            /** @example 1673332556 */
            issueTime?: number;
          };
        };
      };
    };
    /** @description OK */
    CardNotionAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CardNotionAdd"];
        };
      };
    };
    /** @description OK */
    CardRedDotRes: {
      content: {
        "application/json": components["schemas"]["ApiArrOkRes"] & {
          /** @description 有新消息的卡片id列表 */
          data?: number[];
        };
      };
    };
    /** @description OK */
    CardListInfoRes: {
      content: {
        "application/json": components["schemas"]["ApiArrOkRes"] & {
          /**
           * @description 返回值,0正常, 其他皆为异常
           * @example 0
           */
          code?: number;
          /** @description 卡片列表详情信息 */
          data?: components["schemas"]["CardInfo"][];
        };
      };
    };
    /** @description OK */
    CardInfoRes: {
      content: {
        "application/json": components["schemas"]["ApiOkRes"] & {
          data?: components["schemas"]["CardInfo"];
        };
      };
    };
    /** @description OK */
    CardLikeIdListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            ids: number[];
          };
        };
      };
    };
    /** @description OK */
    CardLikeListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /** @description 点赞卡片列表 */
            list: components["schemas"]["CardLikeItem"][];
          };
        };
      };
    };
    /** @description OK */
    CardNotionShowRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["CardNotionItemDetailShow"];
        };
      };
    };
    /** @description OK */
    CardNotionListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /** @description 想法列表 */
            list: components["schemas"]["CardNotionItemShow"][];
            card?: components["schemas"]["CardInfo"];
            /**
             * @description 想法数量
             * @example 3
             */
            count: number;
          };
        };
      };
    };
    /** @description OK */
    CardNotificationNewNumRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /**
             * @description 新消息数量数量
             * @example 3
             */
            count: number;
          };
        };
      };
    };
    /** @description OK */
    CardNotificationListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /** @description 通知列表 */
            list: components["schemas"]["CardNotificationShow"][];
            card?: components["schemas"]["CardInfo"];
            /**
             * @description 通知数量
             * @example 3
             */
            count: number;
          };
        };
      };
    };
    /** @description OK */
    CardNotionCommentListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /** @description 想法列表 */
            list: components["schemas"]["CardNotionCommentShow"][];
            card?: components["schemas"]["CardInfo"];
            /**
             * @description 想法数量
             * @example 3
             */
            count: number;
          };
        };
      };
    };
    /** @description OK */
    CardIsEnableRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: {
            /**
             * @description 是否对玩家开启了剧情卡功能
             * @example true
             */
            isEnable?: boolean;
          };
        };
      };
    };
    /** @description OK */
    CardDiscoverRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: number[];
        };
      };
    };
    /** @description OK */
    EquipWeaponsRankRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["EquipWeaponsRankData"];
        };
      };
    };
    /** @description OK */
    ComplainAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["ComplainAddData"];
        };
      };
    };
    /** @description OK */
    WeaponFiltersRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["WeaponFilterData"];
        };
      };
    };
    /** @description OK */
    EquipCommentListRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["EquipCommentListData"];
        };
      };
    };
    /** @description OK */
    SlientLoveAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["SlientLoveAdd"];
        };
      };
    };
    /** @description OK */
    ApiActionRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["ApiAction"];
        };
      };
    };
    /** @description OK */
    ApiOkRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["ApiOkData"];
        };
      };
    };
    /** @description OK */
    GetLocationRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["GetLocationData"];
        };
      };
    };
    /** @description OK */
    CommonAddRes: {
      content: {
        "application/json": {
          /** @example 0 */
          code: number;
          data: components["schemas"]["CommonAdd"];
        };
      };
    };
    /** @description OK */
    "res-inform-list": {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            list?: components["schemas"]["inform"][];
          };
        };
      };
    };
    /** @description OK */
    "res-inform-news-num": {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: {
            /**
             * @description 新通知数
             * @example 0
             */
            inform?: number;
            /**
             * @description 新消息数
             * @example 0
             */
            message?: number;
            newinform?: components["schemas"]["inform"];
          };
        };
      };
    };
    /** @description OK */
    "res-moment-lottery-show": {
      content: {
        "application/json": {
          /** @example 0 */
          code?: number;
          data?: components["schemas"]["moment-lottery-show"];
        };
      };
    };
  };
  parameters: {
    /** @description 搜索关键词 */
    atPlayersKw?: string;
    /** @description 关联卡片id列表(CSV格式) */
    cardIds: string;
    /** @description 角色等级 */
    level: number;
    /** @description 卡片id */
    cardId: number;
    /** @description 评论id */
    commentId: number;
    /** @description 卡片想法id */
    cardNotionId: number;
    /** @description 卡片评论id */
    cardNotionCommentId: number;
    /** @description 卡片想法评论文本 */
    cardNotionText: string;
    /** @description 回复的评论id */
    replyCommentId?: number;
    /** @description 被举报玩家角色id */
    complainTargetUser: number;
    /** @description 举报资源类型 card_notion => 剧情卡想法;  card_notion_comment => 剧情卡评论 */
    complainResouceType: string;
    /** @description 举报资源类型是card_notion的时候就是想法id, 评论的时候就是评论id, 方便定位举报资源 */
    complainResouceId: number;
    /** @description 举报文本 */
    complainContent: string;
    /** @description 举报理由 */
    complainReason: string;
    /** @description 活动名字 */
    activityName: string;
    /** @description 时装道具等级 精致， 豪华，至尊 */
    fashionLotteryItemLevel?: number;
    /** @description 账号 */
    cloudGameAccount: string;
    /** @description 角色id, 不存在可传0 */
    cloudGameRoleId: number;
    /** @description 时间戳, 单位s */
    cloudGameTime: number;
    /** @description 校验token token计算方式为 md5(time + account + roleid + FACE_PINCH_CLOUD_AUTH_SALT) */
    cloudGameAuthToken: string;
    /** @description 校验token token计算方式为 md5(time + account + roleid + FACE_PINCH_DASHEN_AUTH_SALT) */
    dashenAuthToken: string;
    /** @description 职业id */
    jobId?: number;
    /** @description 性别 */
    gender?: number;
    /** @description 查询的日期 (默认查前一日榜单) 格式 yyyy-MM-dd */
    qmpkDate?: string;
    /** @description 通知id */
    notificationId: number;
    /** @description 通知状态 0 => 未读 1 => 已读 */
    readType?: 0 | 1;
    /** @description 搜索关键字 */
    kw?: string;
    /** @description 角色id */
    roleId: number;
    /** @description 图片id */
    chat_photo_id: number;
    /** @description 图片id(csv格式) */
    chat_photo_ids: string;
    /** @description 图片url */
    chat_url: string;
    /** @description 图片授权token */
    chat_auth_token: string;
    /** @description nos图片类型 */
    chat_nos_type: string;
    /**
     * @description #### 登录模式
     * -------
     *   注意变量名前的双下划线, 防止和游戏内自由携带的参数名字冲突可能性
     *
     * -  默认是 *legacy* 模式，兼容现有实现(不指定该参数即为该模式), 跳转的地址配置在302的 location 字段中, ios内置浏览器使用该模式有概率会出现 cookie 丢失的问题
     * -  新的 *jsbridge* 模式，不在使用302跳转，前端使用 jsbridge 和 unisdk 通信使用原生代码来维持 cookie 状态, 此时无需使用302配置 location 跳转， 携带 location 返回 json 格式即可, 由前端自由控制路由
     */
    activityLoginMode?: "legacy" | "jsbridge";
    /** @description skey */
    skey?: string;
    /** @description 搜索关键词 */
    facePinchKw?: string;
    /** @description 捏脸站排序方式 */
    facePinchSortBy?: "hot" | "weekHot" | "new";
    /** @description 日期 */
    dateStr?: string;
    /** @description 捏脸站分享id */
    facePinchShareId?: string;
    /** @description 捏脸分享的视频id */
    facePinchShareVideoId?: string;
    /** @description 捏脸站作品id */
    facePinchWorkId?: number;
    /** @description 捏脸站分享视频url */
    facePinchShareVideo?: string;
    /** @description 公开级别， 0 => 公开 1 => 私有 */
    facePinchVisibility?: 0 | 1;
    /** @description 筛选可用部位：1=塑形，2=妆容，4=全脸，可组合使用位运算 */
    facePinchFilterParts?: number;
    /** @description index */
    index?: number;
    /** @description 服务器id */
    serverid: number;
    /** @description 目标玩家id */
    targetid?: number;
    /** @description 目标玩家ids(csv) */
    cacelTargetIds?: string;
    /** @description 目标玩家id数组 */
    targetids?: string;
    /** @description 回复的玩家id */
    replyid?: number;
    /** @description 心情id */
    momentid: number;
    /** @description 心情id */
    momentId: number;
    /** @description 获取比评论id小的评论列表 */
    commentid: number;
    /** @description 评论id */
    commentid2: number;
    /** @description 帮会id */
    guildid: number;
    /** @description 文本 */
    text?: string;
    /** @description 文本 */
    textRequired: string;
    /** @description 时间戳(ms) 非必选，不传的时候使用当前时间戳，兼容现有实现 */
    tsOpt?: number;
    likeAction?: "do" | "undo";
    /** @description 返回比该id小的数据 */
    lastid?: number;
    /** @description 指返回里的hot_index, 从1开始计数 */
    hotIndex?: number;
    /** @description 页码 */
    page?: number;
    sortBy?: "new" | "hot";
    /** @description 每页大小 */
    pageSize?: number;
    /** @description 每页大小 */
    pagesize?: number;
    /** @description 每页大小 */
    page_size?: number;
    /** @description 每页大小 */
    page_size_style?: number;
    nosType: "photo" | "moment" | "avatar" | "video" | "tape" | "screenshot" | "fireworks" | "report_log";
    /** @description 返回token格式，适配前端组件格式选择web */
    nosStyle: "default" | "web";
    nosExtName: "jpg" | "jpeg" | "png" | "log";
    textStyle: "plain" | "html";
    /**
     * @description | 枚举值  | 含义  |
     * |---|---|
     * | 0  | 标记为正常  |
     * | -1  | 标记为删除  |
     */
    accountStatus?: 0 | -1;
    m_title?: string;
    m_content?: string;
    imglist?: string;
    videolist?: string;
    /**
     * @description | type | meaning |
     * | ------------- |:-------------:|
     * | 1 | 踩空间 |
     * | 2       | 送礼 |
     * | 3 | 送鲜花 |
     */
    eventType?: 1 | 2 | 3;
    parameter?: string;
    server_id: number;
    date_yyyymmdd?: string;
    equip_period?: number;
    equip_position: number;
    /** @description 装备id */
    equipId: string;
    eventtoken?: string;
    time: number;
    /** @description 心愿id */
    wishId: string;
    /** @description 设备标志id */
    deviceId: string;
    /** @description 助力文本 */
    helpText: string;
    /** @description 助力ID */
    helpId: number;
    /** @description url */
    nosUrl: string;
    /** @description 道具id */
    templateId?: number;
    /** @description 商品数量 */
    num?: number;
    /** @description 总体进度 */
    totalProgress?: number;
    /** @description 开始时间 */
    startTime?: number;
    /** @description 结束时间 */
    endTime?: number;
    /** @description 随机字符串(6位) */
    nonce?: string;
    /** @description 查询登录的日期 (yyyy-MM-dd) */
    ds?: string;
    /** @description 当前时间戳(ms) */
    ts?: number;
    /** @description 助力进度 */
    process?: number;
    /** @description 心愿单类型  0纯文本  1道具 */
    wishType?: 0 | 1;
    /** @description 心愿单状态 0 默认，玩家可以助力、删除等操作; 1已领奖; 2, 已删除;  3, 已返还; */
    wishStatus?: 0 | 1 | 2 | 3;
    /** @description 角色id */
    roleid: number;
    /** @description 时间戳(ms) */
    timestamp: number;
    /**
     * @description | 排序选项 | 说明 |
     * |---------|------|
     * | requestPlayCount | 点播量降序 |
     * | rating | 评分降序 |
     * | recentHot | 新晋热门降序, 周一零点开始计算, 用本周点播次数降序 |
     */
    "music-club-list-sort"?: "requestPlayCount" | "rating" | "recentHot";
    /** @description 乐团id, 用于实现我的乐团唱片功能，游戏需要传入我的乐团id来过滤出我的乐团唱片 */
    "music-club-id-filter"?: number;
    /** @description 页码 */
    commonPage?: number;
    /** @description 每页大小 */
    commonPageSize?: number;
    /** @description 乐团id */
    "music-club-id"?: number;
    /** @description 唱片上架后生成的发行id */
    "music-club-recording-id"?: number;
    /** @description 服务器id */
    server: number;
    /** @description 请求时间戳,单位毫秒 */
    authTime: number;
    /** @description 用户唯一账号 */
    authAccount: string;
    authUrs: string;
    /** @description 账号id,包含渠道信息 */
    authAccountId: string;
    /** @description 角色等级 */
    roleLevel: number;
    /** @description 取值为 "cn", "vn", "en", "th", "ina"（中文/越南文/英文/泰文/印尼语） */
    language?: "cn" | "vn" | "en" | "th" | "ina";
    /** @description 国家代码或地区标识 */
    country?: number;
    /** @description token计算方式为 md5(time + account + urs + roleId + AUTH_TOKEN_SALT) */
    authToken: string;
    /** @description 角色id */
    authRoleId: number;
    /** @description 传参数一般接口列表最后一项的id, 用于下拉滑动获取更多, 倒叙排序下会返回比该id小的数据, 第一次传0即可 */
    lastid2: number;
    /** @description 每页大小 */
    "inform-pagesize"?: number;
  };
  requestBodies: never;
  headers: never;
  pathItems: never;
}

export type $defs = Record<string, never>;

export type external = Record<string, never>;

export interface operations {

  /** 百相演奏-乐团-创建  (服务端调用) */
  musicClubCreate: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["music-club-create-req"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["music-club-create-resp"];
        };
      };
    };
  };
  /** 百相演奏-乐团-解散  (服务端调用) */
  musicClubDisband: {
    parameters: {
      query: {
        /** @description 乐团ID */
        musicClubId: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["music-club-disband-resp"];
        };
      };
    };
  };
  /**
   * 百相演奏-乐团-详情
   * @description 获取乐团详情, 返回主打唱片，当前上架唱片数量等信息，注意，这里不校验乐团id，因为服务器请求是，乐团信息可能未同步完成，此时返回符合数据结构的空信息
   */
  musicClubShow: {
    parameters: {
      query: {
        musicClubId: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /**
             * Format: int32
             * @description 状态码
             * @example 0
             */
            code: number;
            data: components["schemas"]["music-club-show-resp"];
          };
        };
      };
    };
  };
  /** 百相演奏-乐团-更新 (服务端调用) */
  musicClubUpdate: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["music-club-update-req"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["music-club-update-resp"];
        };
      };
    };
  };
  /**
   * 百相演奏-排行-最热乐团总榜
   * @description 总热度榜：展示50名。总热度从高到低排序。
   */
  getMusicClubRankHotList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              list: components["schemas"]["music-club-rank-item"][];
              /**
               * @description 排行刷新时间(ms)
               * @example 1716835200000
               */
              refreshTime: number;
            };
          };
        };
      };
    };
  };
  /**
   * 百相演奏-排行-最热乐团总榜 (服务端调用)
   * @description 总热度榜：展示10名。总热度从高到低排序。
   */
  getMusicClubRankHotListForServer: {
    parameters: {
      query: {
        /** @description 周时间戳(yyyyMMdd), 使用每周的周一作为周时间标记 */
        weekDs: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              list: components["schemas"]["music-club-rank-item"][];
              /**
               * @description 排行刷新时间(ms)
               * @example 1716835200000
               */
              refreshTime: number;
            };
          };
        };
      };
    };
  };
  /**
   * 百相演奏-排行-新晋乐团榜单
   * @description 新晋榜：展示20名。本周（从周一零点开始算）获取的总热度值最高的队伍。
   */
  getMusicClubRankWeekHotList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        timestamp: components["parameters"]["timestamp"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              list: components["schemas"]["music-club-rank-item"][];
              /**
               * @description 排行刷新时间(ms)
               * @example 1716835200000
               */
              refreshTime: number;
              /**
               * @description 周时间戳(yyyyMMdd)
               * @example 20250603
               */
              weekDs: string;
            };
          };
        };
      };
    };
  };
  /**
   * 百相演奏-排行-新晋乐团榜单 (服务端调用)
   * @description 新晋榜：展示10名。当前周weekDs（从周一零点开始算）获取的总热度值最高的队伍。
   */
  getMusicClubRankWeekHotListForServer: {
    parameters: {
      query: {
        /** @description 周时间戳(yyyyMMdd), 使用每周的周一作为周时间标记 */
        weekDs: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              list: components["schemas"]["music-club-rank-item"][];
              /**
               * @description 排行刷新时间(ms)
               * @example 1716835200000
               */
              refreshTime: number;
              /**
               * @description 周时间戳(yyyyMMdd)
               * @example 20250603
               */
              weekDs: string;
            };
          };
        };
      };
    };
  };
  /**
   * 百相演奏-点播-唱片列表
   * @description 展示唱片列表，用于游戏内点播节目的信息展示
   */
  getMusicClubRecordingList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        sortBy?: components["parameters"]["music-club-list-sort"];
        musicClubId?: components["parameters"]["music-club-id-filter"];
        page?: components["parameters"]["commonPage"];
        pageSize?: components["parameters"]["commonPageSize"];
        /** @description 搜索关键词, 用于搜索唱片名称 */
        kw?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              list: components["schemas"]["music-club-recording-show"][];
            };
          };
        };
      };
    };
  };
  /**
   * 百相演奏-点播-同步点播操作行为 (服务端调用)
   * @description 游戏同步点播操作行为，用于维护增加唱片点播计数
   */
  musicClubRadioRequestPlay: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    /** @description 点播请求 */
    requestBody: {
      content: {
        "application/json": components["schemas"]["music-club-radio-request-play-req"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["music-club-radio-request-play-resp"];
          };
        };
      };
    };
  };
  /**
   * 唱片审核回调
   * @description 外部审核系统完成审核后调用此接口通知审核结果
   */
  musicClubRecordingAuditCallback: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["music-club-recording-audit-callback-req"];
      };
    };
    responses: {
      /** @description 审核回调处理成功 */
      200: {
        content: {
          "application/json": components["schemas"]["music-club-recording-audit-callback-resp"];
        };
      };
    };
  };
  /**
   * 百相演奏-唱片-上架
   * @description 上架唱片，从本地唱片上架到乐团
   */
  musicClubRecordingRelease: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    /** @description 上架唱片 */
    requestBody: {
      content: {
        "application/json": components["schemas"]["music-club-recording-release-req"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["music-club-recording-release-resp"];
          };
        };
      };
    };
  };
  /**
   * 百相演奏-唱片-下架
   * @description 下架唱片，从乐团中移除该唱片, 只有乐团经理可以下架唱片, 需要*游戏服务端调用*, 完成乐团经理的权限校验
   */
  musicClubRecordingRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        musicClubId?: components["parameters"]["music-club-id"];
        recordingId?: components["parameters"]["music-club-recording-id"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["music-club-recording-remove-resp"];
          };
        };
      };
    };
  };
  /**
   * 百相演奏-唱片-打分 (服务端接口)
   * @description 给唱片打分，打分范围0-10分
   */
  musicClubRecordingRate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    /** @description 唱片打分请求 */
    requestBody: {
      content: {
        "application/json": components["schemas"]["music-club-recording-rate-req"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["music-club-recording-rate-resp"];
          };
        };
      };
    };
  };
  /**
   * 百相演奏-唱片-详情 (服务端接口)
   * @description 获取唱片详细信息，包括乐团信息、点播次数、评分等
   */
  musicClubServerRecordingShow: {
    parameters: {
      query: {
        /** @description 唱片ID */
        recordingId: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["music-club-recording-show"];
          };
        };
      };
    };
  };
  /**
   * 百相演奏-唱片-详情
   * @description 获取唱片详细信息，包括乐团信息、点播次数、评分等
   */
  musicClubRecordingShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 唱片ID */
        recordingId: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["music-club-recording-show"];
          };
        };
      };
    };
  };
  /**
   * 乐团-热度变化日志
   * @description 处理来自游戏的乐团热度变化日志，用于更新乐团总榜单和周榜单。
   *
   * 日志来源：杨凯 游戏程序 <EMAIL>
   *
   * 日志格式示例：
   * ```
   * [2025-06-25 21:55:20][BandHeat],{"server":"1", "band_id":"300001", "band_name":"yk的乐团", "band_level":3, "heat":60, "week_heat":50, "time":1750859718, "show_amount":10, "recording_amount":0, "u_dtls":[]}
   * ```
   */
  kafkaMusicClubBandHeat: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 服务器ID
           * @example 1
           */
          server: string;
          /**
           * @description 乐团ID
           * @example 300001
           */
          band_id: string;
          /**
           * @description 乐团名称
           * @example yk的乐团
           */
          band_name: string;
          /**
           * @description 乐团等级
           * @example 3
           */
          band_level: number;
          /**
           * @description 乐团总热度
           * @example 60
           */
          heat: number;
          /**
           * @description 乐团本周获取的热度（每周一0点重置）
           * @example 50
           */
          week_heat: number;
          /**
           * @description 这次获取的时间戳（秒），用这个时间判断所在的周
           * @example 1750859718
           */
          time: number;
          /**
           * @description 本次演出加的热度
           * @example 10
           */
          show_amount?: number;
          /**
           * @description 本次唱片加的热度
           * @example 0
           */
          recording_amount?: number;
          /**
           * @description 用户详情列表
           * @example []
           */
          u_dtls?: Record<string, never>[];
        };
      };
    };
    responses: {
      /** @description 处理成功 */
      200: {
        content: {
          "application/json": {
            /**
             * @description 响应码，0表示成功
             * @example 0
             */
            code?: number;
            /** @description 处理结果数据 */
            data?: {
              /** @description 乐团总热度更新结果 */
              updateHotRet?: {
                /**
                 * @description 影响的行数
                 * @example 1
                 */
                affectedRows?: number;
                /**
                 * @description 改变的行数
                 * @example 1
                 */
                changedRows?: number;
                /**
                 * @description 插入ID
                 * @example 0
                 */
                insertId?: number;
              };
              /** @description 乐团周热度更新结果 */
              updateWeekHotRet?: {
                /**
                 * @description 影响的行数
                 * @example 1
                 */
                affectedRows?: number;
                /**
                 * @description 改变的行数
                 * @example 1
                 */
                changedRows?: number;
                /**
                 * @description 插入ID
                 * @example 0
                 */
                insertId?: number;
              };
            };
          };
        };
      };
      /** @description 请求参数错误 */
      400: {
        content: never;
      };
      /** @description 服务器内部错误 */
      500: {
        content: never;
      };
    };
  };
  /** 信用分日志 */
  kafkaCreditscore: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 服务器ID
           * @example 2010
           */
          server: string;
          /**
           * @description 账号ID
           * @example aebfpp4tlufvpc7z@ios.app_store.win.163.com
           */
          account_id: string;
          /**
           * @description 角色ID
           * @example **********
           */
          role_id: string;
          /**
           * @description 角色名称
           * @example 卿若惜
           */
          role_name: string;
          /**
           * @description 角色等级
           * @example 157
           */
          role_level: number;
          /**
           * @description 用户ID
           * @example aebfpp4tlufvpc7z
           */
          uid: string;
          /**
           * @description mpay账号
           * @example <EMAIL>
           */
          mpay_account: string;
          /**
           * @description IP地址
           * @example **************
           */
          ip: string;
          /**
           * @description 信用分
           * @example 667
           */
          credit_score: number;
          /**
           * @description 所有分
           * @example 500
           */
          all_score: number;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 角色登录额外信息日志 */
  kafkaLoginroleAdditional: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description IP地址
           * @example ************
           */
          ip: string;
          /**
           * @description IPv6地址
           * @example
           */
          ipv6?: string;
          /**
           * @description 设备型号
           * @example Handheld#iPad11,3#6#Metal
           */
          device_model: string;
          /**
           * @description 设备高度
           * @example 1668
           */
          device_height: number;
          /**
           * @description 设备宽度
           * @example 2224
           */
          device_width: number;
          /**
           * @description 操作系统名称
           * @example ios
           */
          os_name: string;
          /**
           * @description 操作系统版本
           * @example iOS 13.7
           */
          os_ver: string;
          /**
           * @description MAC地址
           * @example 5FCF4F44-033E-4F2F-B07B-ECF9E94586B0
           */
          mac_addr: string;
          /**
           * @description 设备唯一标识
           * @example A11E6BFA-628F-4441-8952-54A87FE8718A
           */
          udid: string;
          /**
           * @description 国家代码
           * @example 86
           */
          nation: number;
          /**
           * @description 网络服务提供商
           * @example
           */
          isp?: string;
          /**
           * @description 网络类型
           * @example wifi
           */
          network: string;
          /**
           * @description 应用渠道
           * @example app_store
           */
          app_channel: string;
          /**
           * @description 应用版本
           * @example 641100
           */
          app_ver: string;
          /**
           * @description 服务器ID
           * @example 2043
           */
          server: string;
          /**
           * @description 账号ID
           * @example aebflnbaqab6gh2h@ios.app_store.win.163.com
           */
          account_id: string;
          /**
           * @description 旧账号ID
           * @example
           */
          old_accountid?: string;
          /**
           * @description 角色ID
           * @example **********
           */
          role_id: string;
          /**
           * @description 角色名称
           * @example .。红衣洛洛
           */
          role_name: string;
          /**
           * @description 角色等级
           * @example 154
           */
          role_level: number;
          /**
           * @description VIP等级
           * @example 6
           */
          u_vip?: number;
          /**
           * @description 用户图标
           * @example 1
           */
          u_icon?: string;
          /**
           * @description 用户学校
           * @example 1
           */
          u_sch?: string;
          /**
           * @description 幸运值
           * @example 301
           */
          mf?: number;
          /**
           * @description 修为值
           * @example 118
           */
          xiuwei?: number;
          /**
           * @description 修炼值
           * @example 55
           */
          xiulian?: number;
          /**
           * @description 当前师傅
           * @example 0
           */
          current_shifu?: string;
          /**
           * @description 当前徒弟
           * @example 28607202047,20391602043
           */
          current_tudi?: string;
          /**
           * @description 所有师傅
           * @example [
           *   621102044,
           *   652002044
           * ]
           */
          all_shifu?: number[];
          /**
           * @description 所有徒弟
           * @example [
           *   578302044,
           *   1637002043
           * ]
           */
          all_tudi?: number[];
          /**
           * @description 侠侣ID
           * @example 0
           */
          partner_id?: string;
          /**
           * @description 是否常用设备
           * @example 1
           */
          isCommonDevice?: number;
          /**
           * @description 用户详细信息
           * @example {
           *   "monthcard_left": 16,
           *   "jade_left": 5223
           * }
           */
          u_dtls?: Record<string, never>;
          /**
           * @description 国家地区编号
           * @example CN
           */
          country_code?: string;
          /**
           * @description 角色仙凡身等级取较大值
           * @example 154
           */
          max_level?: number;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 玩家升级日志 */
  kafkaPlayerlevelup: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 服务器ID
           * @example 2483
           */
          server: string;
          /**
           * @description 账号ID
           * @example <EMAIL>
           */
          account_id: string;
          /**
           * @description 旧账号ID
           * @example
           */
          old_accountid?: string;
          /**
           * @description 角色ID
           * @example **********
           */
          role_id: string;
          /**
           * @description 角色名称
           * @example 子夜
           */
          role_name: string;
          /**
           * @description 角色等级
           * @example 2
           */
          role_level: number;
          /**
           * @description VIP等级
           * @example 0
           */
          u_vip?: number;
          /**
           * @description 用户图标
           * @example 0
           */
          u_icon?: string;
          /**
           * @description 用户学校
           * @example 9
           */
          u_sch?: string;
          /**
           * @description 升级原因
           * @example LevelUp
           */
          reason: string;
          /**
           * @description 用户详细信息
           * @example 38
           */
          u_dtls?: number;
          /**
           * @description IP地址
           * @example ***************
           */
          ip?: string;
          /**
           * @description IPv6地址
           * @example
           */
          ipv6?: string;
          /**
           * @description 设备型号
           * @example Handheld#HONOR PGT-AN10#8#OpenGLES3
           */
          device_model?: string;
          /**
           * @description 设备高度
           * @example 1080
           */
          device_height?: number;
          /**
           * @description 设备宽度
           * @example 2344
           */
          device_width?: number;
          /**
           * @description 操作系统名称
           * @example ad
           */
          os_name?: string;
          /**
           * @description 应用渠道
           * @example netease.sub40406_toutiaoys3_cpc_dec
           */
          app_channel?: string;
          /**
           * @description 操作系统版本
           * @example Android OS 15 / API-35 (HONORPGT-AN10/9.
           */
          os_ver?: string;
          /**
           * @description 在线时长
           * @example 197
           */
          online_time?: string;
          /**
           * @description 是否PC端登录
           * @example 0
           */
          pc_login?: number;
          /**
           * @description 角色仙凡身等级取较大值
           * @example 2
           */
          max_level?: number;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 健康检查 */
  healthCheck: {
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取动态允许的标签id列表 */
  momentTags: {
    responses: {
      200: components["responses"]["MomentTagsRes"];
    };
  };
  /** 玩家动态at列表 */
  atPlayersList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["AtPlayersListRes"];
    };
  };
  /** 玩家动态at玩家搜索 */
  atPlayersSearch: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        kw?: components["parameters"]["atPlayersKw"];
      };
    };
    responses: {
      200: components["responses"]["AtPlayersListRes"];
    };
  };
  /** 时装抽奖获取记录列表 */
  fashionLotteryList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        itemLevel?: components["parameters"]["fashionLotteryItemLevel"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FashionLotteryListRes"];
    };
  };
  /** 时装抽奖长期类型获取记录列表 */
  fashionLotteryLongTermList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        itemLevel?: components["parameters"]["fashionLotteryItemLevel"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FashionLotteryListRes"];
    };
  };
  /**
   * 捏脸-分享-信息
   * @description 捏脸站作品公开的分享信息
   */
  facePinchWebPublicShareInfo: {
    parameters: {
      query?: {
        shareId?: components["parameters"]["facePinchShareId"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWebPublicShareInfoRes"];
    };
  };
  /**
   * 捏脸-视频-分享信息
   * @description 游戏内捏脸视频分享信息
   */
  facePinchWebPublicShareVideoInfo: {
    parameters: {
      query?: {
        shareId?: components["parameters"]["facePinchShareVideoId"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWebPublicShareVideoInfoRes"];
    };
  };
  /**
   * 捏脸-作品-公开列表
   * @description 捏脸站作品公开列表
   */
  facePinchWorkListPublic: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        gender?: components["parameters"]["gender"];
        jobId?: components["parameters"]["jobId"];
        date?: components["parameters"]["dateStr"];
        sortBy?: components["parameters"]["facePinchSortBy"];
        filterParts?: components["parameters"]["facePinchFilterParts"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkListRes"];
    };
  };
  /**
   * 捏脸-作品-收藏列表
   * @description 捏脸站作品玩家自己收藏列表
   */
  facePinchWorkListCollect: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        filterParts?: components["parameters"]["facePinchFilterParts"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkListCollectRes"];
    };
  };
  /**
   * 捏脸-作品-个人列表
   * @description 捏脸站作品玩家自己的设计作品
   */
  facePinchWorkListSelf: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        filterParts?: components["parameters"]["facePinchFilterParts"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkListSelfRes"];
    };
  };
  /**
   * 捏脸-作品-分享获取
   * @description 通过分享id获取作品
   */
  facePinchWorkGetByShareId: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        shareId?: components["parameters"]["facePinchShareId"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkGetByShareIdRes"];
    };
  };
  /**
   * 捏脸-作品-详情
   * @description 查看单个作品
   */
  facePinchWorkDetail: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkDetailRes"];
    };
  };
  /**
   * 捏脸-作品-更新可见性
   * @description 修改作品的可见状态
   */
  facePinchWorkUpdateVisibility: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
        visibility?: components["parameters"]["facePinchVisibility"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkUpdateVisibilityRes"];
    };
  };
  /**
   * 捏脸-FP令牌-获取
   * @description 获取捏脸站fp上传token
   */
  facePinchGetFpToken: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchGetFpTokenRes"];
    };
  };
  /**
   * 捏脸-作品-搜索
   * @description 支持玩家搜索到所有已被公开的作品，不限制职业和性别，但不包含仅个人可见的作品。
   */
  facePinchWorkSearch: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        kw?: components["parameters"]["facePinchKw"];
        gender?: components["parameters"]["gender"];
        jobId?: components["parameters"]["jobId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkListRes"];
    };
  };
  /**
   * 捏脸-云游戏-登录
   * @description 云游戏捏脸相关接口登录, 获取skey接口凭证
   * - account使用云游戏登录的账号字段, roleid无可传0,
   * - token计算依赖的 FACE_PINCH_CLOUD_AUTH_SALT 联系 <EMAIL> 获取
   */
  facePinchCloudGameAuthLogin: {
    parameters: {
      query: {
        roleid: components["parameters"]["cloudGameRoleId"];
        account: components["parameters"]["cloudGameAccount"];
        time: components["parameters"]["cloudGameTime"];
        token: components["parameters"]["cloudGameAuthToken"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchCloudGameAuthLoginRes"];
    };
  };
  /**
   * 捏脸-数据-导入
   * @description 导入捏脸数据到指定roleId
   */
  facePinchDashenShareInfoImport: {
    parameters: {
      query: {
        shareId?: components["parameters"]["facePinchShareId"];
        roleId: components["parameters"]["roleId"];
        server: components["parameters"]["server"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 捏脸-作品-新增
   * @description 新增一个玩家的捏脸作品数据
   */
  facePinchWorkAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
      };
    };
    /** @description 新增捏脸作品 */
    requestBody: {
      content: {
        "application/json": components["schemas"]["FacePinchWorkAdd"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchWorkAddRes"];
    };
  };
  /**
   * 捏脸-视频-新增分享
   * @description 给分享视频生成一个分享id
   */
  facePinchShareVideoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        video?: components["parameters"]["facePinchShareVideo"];
      };
    };
    responses: {
      200: components["responses"]["FacePinchShareVideoAddRes"];
    };
  };
  /**
   * 捏脸-作品-删除
   * @description 删除一个玩家的捏脸作品数据
   */
  facePinchWorkDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 捏脸-作品-应用
   * @description 应用该捏脸作品, 用于捏脸作品被应用于预创建计数(热度计算因子之一)
   */
  facePinchWorkApply: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 捏脸-作品-点赞
   * @description 点赞捏脸作品
   */
  facePinchWorkLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 捏脸-作品-取消点赞
   * @description 取消点赞捏脸作品
   */
  facePinchWorkCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 捏脸-作品-收藏
   * @description 收藏捏脸作品
   */
  facePinchWorkCollect: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 捏脸-作品-取消收藏
   * @description 取消收藏捏脸作品
   */
  facePinchWorkCancelCollect: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        id?: components["parameters"]["facePinchWorkId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 更新玩家的头像框 */
  playerExpressionBaseUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
      };
    };
    /** @description expression base */
    requestBody: {
      content: {
        "application/json": components["schemas"]["ExpressionBase"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 那年今日是否发表过动态
   * @description 玩家在往年的同一日期至少发布过一条动态的时候返回为true
   */
  thisDayThatYearMomentHas: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["ThisDayThatYearHasMomentRes"];
    };
  };
  /**
   * 那年今日的动态列表
   * @description 返回那年同一日的动态列表，按照时间倒序
   */
  thisDayThatYearMomentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pagesize?: components["parameters"]["pagesize"];
      };
    };
    responses: {
      200: components["responses"]["ThisDayThatYearHasMomentListRes"];
    };
  };
  /** 添加举报日志 */
  complainAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        targetid: components["parameters"]["complainTargetUser"];
        resouce_type: components["parameters"]["complainResouceType"];
        resouce_id: components["parameters"]["complainResouceId"];
        content: components["parameters"]["complainContent"];
        reason: components["parameters"]["complainReason"];
      };
    };
    responses: {
      200: components["responses"]["ComplainAddRes"];
    };
  };
  /**
   * 添加兵器谱弹幕
   * @description 发言限制: *每个玩家每个武器1分钟限制1条弹幕*
   */
  equipCommentsCreate: {
    parameters: {
      query: {
        equipId: components["parameters"]["equipId"];
        text?: components["parameters"]["text"];
      };
    };
    responses: {
      200: components["responses"]["ApiOkRes"];
    };
  };
  /** 兵器谱弹幕装备列表 */
  equipCommentsList: {
    parameters: {
      query: {
        equipId: components["parameters"]["equipId"];
      };
    };
    responses: {
      200: components["responses"]["EquipCommentListRes"];
    };
  };
  /** 获取兵器谱排行筛选数据 */
  equipWeaponsFilters: {
    responses: {
      200: components["responses"]["WeaponFiltersRes"];
    };
  };
  /** 获取兵器谱排行 */
  equipWeaponsRank: {
    parameters: {
      query: {
        server_id: components["parameters"]["server_id"];
        equip_position: components["parameters"]["equip_position"];
        page?: components["parameters"]["page"];
        page_size?: components["parameters"]["page_size"];
      };
    };
    responses: {
      200: components["responses"]["EquipWeaponsRankRes"];
    };
  };
  /** 获取奇珍榜排行 */
  equipTreasuresRank: {
    parameters: {
      query: {
        server_id: components["parameters"]["server_id"];
        page?: components["parameters"]["page"];
        page_size?: components["parameters"]["page_size"];
      };
    };
    responses: {
      200: components["responses"]["EquipWeaponsRankRes"];
    };
  };
  /** 剧情卡功能是否对玩家开放 */
  cardIsEnable: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      200: components["responses"]["CardIsEnableRes"];
    };
  };
  /** 卡片通知列表 */
  cardNotificationList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["CardNotificationListRes"];
    };
  };
  /** 卡片通知列表全部清空 */
  cardNotificationDelAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 卡片通知新消息数量 */
  cardNotificationNewNum: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
      };
    };
    responses: {
      200: components["responses"]["CardNotificationNewNumRes"];
    };
  };
  /**
   * 卡片通知列表全部设为已读
   * @deprecated
   */
  cardNotificationReadAll: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /**
   * 卡片通知列表单条已读
   * @deprecated
   */
  cardNotificationRead: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["notificationId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 列出卡片想法评论列表 */
  cardNotionCommentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notionId: components["parameters"]["cardNotionId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["CardNotionCommentListRes"];
    };
  };
  /** 添加卡片想法评论 */
  cardNotionCommentAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notionId: components["parameters"]["cardNotionId"];
        text: components["parameters"]["cardNotionText"];
        replyCommentId?: components["parameters"]["replyCommentId"];
      };
    };
    responses: {
      200: components["responses"]["CommonAddRes"];
    };
  };
  /** 删除卡片想法评论 */
  cardNotionCommentDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        commentId: components["parameters"]["cardNotionCommentId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 列出单个卡片的想法详情 */
  cardNotionShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notionId: components["parameters"]["cardNotionId"];
      };
    };
    responses: {
      200: components["responses"]["CardNotionShowRes"];
    };
  };
  /** 列出单个卡片下想法列表 */
  cardNotionList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
        sort_by?: components["parameters"]["sortBy"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["CardNotionListRes"];
    };
  };
  /** 添加卡片的想法 */
  cardNotionAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
        text?: components["parameters"]["text"];
      };
    };
    responses: {
      200: components["responses"]["CommonAddRes"];
    };
  };
  /** 删除卡片的想法 */
  cardNotionDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notionId: components["parameters"]["cardNotionId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 给卡片想法点赞 */
  cardNotionLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notionId: components["parameters"]["cardNotionId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 给卡片想法取消点赞 */
  cardNotionCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        notionId: components["parameters"]["cardNotionId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 卡片列表统计详情 */
  cardList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_ids: components["parameters"]["cardIds"];
      };
    };
    responses: {
      200: components["responses"]["CardListInfoRes"];
    };
  };
  /** 卡片红点接口(返回所有有新消息的卡片id) */
  cardRedDot: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["CardRedDotRes"];
    };
  };
  /** 点赞卡片 */
  cardLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 取消点赞卡片 */
  cardCancelLike: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        card_id: components["parameters"]["cardId"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 添加对方为暗恋 */
  slientLoveAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      200: components["responses"]["SlientLoveAddRes"];
    };
  };
  /** 取消暗恋 */
  slientLoveCancel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 获取filepick的token */
  filepickGetToken: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 查询是否开启梦岛 */
  infoIsEnableIsland: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取聊天图片 */
  chatPhotoGet: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        photo_id: components["parameters"]["chat_photo_id"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 保存聊天图片 */
  chatPhotoCreate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        url: components["parameters"]["chat_url"];
        auth_token: components["parameters"]["chat_auth_token"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取上传聊天图片nosToken */
  chatNosGettoken: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        type: components["parameters"]["chat_nos_type"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取上传聊天图片免审核nosToken */
  chatNosGetFreeToken: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        type: components["parameters"]["chat_nos_type"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 添加图片到收藏 */
  chatCollectPhotoAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        photo_id: components["parameters"]["chat_photo_id"];
        auth_token: components["parameters"]["chat_auth_token"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 图片收藏列表 */
  chatCollectPhotoList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        auth_token: components["parameters"]["chat_auth_token"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 从收藏删除图片 */
  chatCollectPhotoRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        photo_ids: components["parameters"]["chat_photo_ids"];
        auth_token: components["parameters"]["chat_auth_token"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 官网活动分享 */
  "activity:activityNameShare": {
    parameters: {
      path: {
        activity_bame: components["parameters"]["activityName"];
      };
    };
    /** @description share work info */
    requestBody: {
      content: {
        "application/json": {
          /** @description works List */
          works?: {
              /** @example 1186001000 */
              roleid?: number;
              /** @example 2484 */
              workid?: number;
              /** @example https://nos.netease.com/hi-163-qnm/qnm/mrt2020_img/20200520/15899418048153.jpg */
              cover?: string;
              /** @example <link button=倩影风华,PSHotTalk,2>我正在报名参加2023倩影风华，快来给我投票送花吧！ */
              text?: string;
            }[];
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 年度总结2020点赞最多动态 */
  activityNdzj2020MomentMostLiked: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            code: number;
            data: {
              hasHotMoment: boolean;
              moment: {
                id: number;
                likeCount: number;
                text: string;
                imgList: string[];
                videoList: string[];
                createTime: number;
              };
            };
          };
        };
      };
    };
  };
  /** 年度总结2020助力心愿助力排行 */
  activityNdzj2020WishHelpRank: {
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            code: number;
            data: {
              list: {
                  rank: number;
                  roleId: number;
                  serverId: number;
                  roleName: string;
                  progress: number;
                }[];
            };
          };
        };
      };
    };
  };
  /** 年度总结2020心最近助力心愿列表 */
  activityNdzj2020WishRecentHelps: {
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            code: number;
            data: {
              list: {
                  id: number;
                  roleId: number;
                  roleName: string;
                  targetId: number;
                  targetName: string;
                  createTime: number;
                }[];
              count: number;
            };
          };
        };
      };
    };
  };
  /** 活动自动登录 */
  activityLogin: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        __login_mode?: components["parameters"]["activityLoginMode"];
        /** @description 活动名 */
        activity_name?: string;
        /** @description 自动登录的活动页面, 不使用活动名跳转时，使用该参数控制跳转页面 */
        redirect_url?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 活动自动登录中转页(jsBridge) */
  activityLoginBridge: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        /** @description 活动名 */
        activity_name?: string;
        /** @description 自动登录的活动页面, 不使用活动名跳转时，使用该参数控制跳转页面 */
        redirect_url?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 验证自动登录产生的token并获取相关信息 */
  activityLoginValidateAccessToken: {
    parameters: {
      query?: {
        /** @description 自动登录生成的验证token */
        access_token?: string;
      };
    };
    responses: {
      200: components["responses"]["ActivityLoginValidateAccessTokenRes"];
    };
  };
  /**
   * 活动分享到梦岛
   * @description ## 10参与话题是个特殊的文本链接
   * `<link button=笛子特效,PSHotTalk,12>话题+文案+表情#24#28` <br>
   * `<link button=笛子特效,PSHotTalk,12>`<br>
   * `<link button={话题名字},PSHotTalk,{话题id}>` <br>
   * 按照这个规则来生成， 话题id要等话题管理后台上线了话题才有的。 这两个变量建议放在配置中
   */
  activityAddMoment: {
    /** @description add moment body */
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 玩家id
           * @example 100100001
           */
          roleId?: number;
          /**
           * @description 心情文本
           * @example test to add
           */
          text?: string;
          /** @description 分享图片列表 */
          imgs?: string[];
          /**
           * @description 是否跳过图片审核
           * @example 1
           * @enum {number}
           */
          skipImgAudit?: 0 | 1;
          /**
           * @description 是否触发动态添加到话题事件
           * @enum {number}
           */
          emitTopicEvent?: 0 | 1;
          /** @description 分享视频列表 */
          videos?: string[];
          /**
           * @description 是否跳过视频审核
           * @enum {number}
           */
          skipVideoAudit?: 0 | 1;
          /**
           * @description 是否出现在话题页 (当文本中附带话题链接才生效, 默认出现)
           * @example 1
           * @enum {number}
           */
          showTopicPage?: 0 | 1;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 全面PK排行 */
  activityQmpkRank: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        ds?: components["parameters"]["qmpkDate"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 全面PK排行(PHP后台访问, ip校验) */
  activityQmpkRank2: {
    parameters: {
      query?: {
        ds?: components["parameters"]["qmpkDate"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * GM指令-通知-标记所有未读
   * @description 通过玩家ID将该玩家的所有通知状态设置为未读
   */
  markAllInformUnread: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description 操作成功 */
      200: {
        content: {
          "application/json": {
            /**
             * @description 状态码
             * @example 0
             */
            code?: number;
            data?: {
              /**
               * @description 标记为未读的消息数量
               * @example 10
               */
              markedCount?: number;
            };
          };
        };
      };
    };
  };
  /** GM指令-查找玩家的某条动态 */
  gmCmdMomentSearch: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        kw?: components["parameters"]["kw"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** GM指令-抽奖动态触发开奖 */
  gmCmdMomentLotteryDraw: {
    parameters: {
      query: {
        momentId: components["parameters"]["momentId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** GM指令-用于测试互动后，是否参与成功 */
  gmCmdMomentLotteryAttend: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        momentId: components["parameters"]["momentId"];
        action: "like" | "comment" | "forward" | "follow";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * GM指令-抽奖动态发奖指令
   * @description 手动触发发奖，调用游戏发奖指令, 纯代理，保持上游接口风格
   */
  "gmCmdGmsPostreceivelimit.php": {
    requestBody?: {
      content: {
        "application/json": {
          /** @example drawlotterymoment */
          cmd: string;
          /** @example drawlotterymoment */
          operatorInfo: string;
          /** @example 51 */
          serverId: string;
          /** @example JAUMALtlc2cAAAAA */
          sn: string;
          /** @example 1 */
          type: number;
          /** @example 681500012 */
          srcPlayerId: number;
          /** @example 10328100012,10325000012 */
          winners: string;
          /** @example 21000039,1,9 */
          prizes: string;
          /** @example 432 */
          returnJade: number;
          /** @example */
          reason: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            status: string;
            result: {
              /** @example JAUMALtlc2cAAAAA */
              SN: string;
            };
          };
        };
      };
    };
  };
  /** 设置全面争霸排行名次(只在测试环境可用) */
  gmCmdQmpkSetRank: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 原始服roleId */
        originRoleId?: number;
        /** @description 排名(从1计数) */
        rank?: number;
        ds?: components["parameters"]["qmpkDate"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * GM指令-热门动态-刷新热门
   * @description 更新热门动态缓存, 传入serverIds和tagIds, tagIds和serverIds都可以选择多个
   */
  gmCmdUpdateHotMomentsCache: {
    requestBody?: {
      content: {
        "application/json": {
          tagIds?: number[];
          serverIds?: string[];
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 全面PK分享 */
  activityQmpkShare: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 原始服角色ID */
        origin_roleid?: number;
        text?: components["parameters"]["text"];
        ts?: components["parameters"]["tsOpt"];
        /** @description 全民pk分享图URL */
        url?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 角色登录梦岛
   * @description 角色登录梦岛, 获取接口访问凭证
   */
  authLogin: {
    parameters: {
      query: {
        time: components["parameters"]["authTime"];
        account: components["parameters"]["authAccount"];
        urs: components["parameters"]["authUrs"];
        accountId: components["parameters"]["authAccountId"];
        roleid: components["parameters"]["roleid"];
        server: components["parameters"]["server"];
        level: components["parameters"]["roleLevel"];
        language?: components["parameters"]["language"];
        country?: components["parameters"]["country"];
        token: components["parameters"]["authToken"];
        /**
         * @deprecated
         * @description 【废弃】是否为海外用户, 0: 否, 1: 是
         * （该字段已废弃，请使用 speechLimitStrategy 字段进行精细化控制）
         */
        isOverSea?: 0 | 1;
        /**
         * @description 发言限制策略，使用二进制标志位控制：
         * - 0 (0000): 无限制
         * - 1 (0001): 屏蔽发言 (BLOCK_SPEECH)
         * - 2 (0010): 屏蔽发图 (BLOCK_IMAGE)
         * - 3 (0011): 屏蔽发言和发图 (BLOCK_ALL)
         */
        speechLimitStrategy?: "0" | "1" | "2" | "3";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["auth-login-resp"];
          };
        };
      };
    };
  };
  /** 梦岛设置好友可见 */
  serverPlayerSetProtectMode: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        time: components["parameters"]["time"];
        /** @description 是否开启朋友圈仅好友可见 */
        protectMode: number;
        /** @description md5(roleId + protectMode + time + AUTH_TOKEN_SALT) */
        token: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取每日登录时间(只支持7d之内) */
  dailyLoginLoginTime: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        ds?: components["parameters"]["ds"];
        nonce?: components["parameters"]["nonce"];
        ts?: components["parameters"]["ts"];
        token: string;
      };
    };
    responses: {
      200: components["responses"]["DailyLoginLoginTimeRes"];
    };
  };
  /**
   * 标记游戏角色状态
   * @description 验证参数和方式同梦岛登录接口
   */
  authMarkRoleStatus: {
    parameters: {
      query: {
        time: components["parameters"]["authTime"];
        account: components["parameters"]["authAccount"];
        urs: components["parameters"]["authUrs"];
        roleid: components["parameters"]["authRoleId"];
        token: components["parameters"]["authToken"];
        status?: components["parameters"]["accountStatus"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取玩家心情列表 */
  activityGetPlayerMoments: {
    parameters: {
      query?: {
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description ok */
      200: {
        content: never;
      };
    };
  };
  /** 问卷系统回调接口 */
  activitySurveySubmit: {
    parameters: {
      query: {
        question: string;
        url: string;
        serverid?: number;
      };
    };
    responses: {
      /** @description ok */
      200: {
        content: never;
      };
    };
  };
  /** @description 添加留言 */
  addmessage: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        replyid?: components["parameters"]["replyid"];
        text?: components["parameters"]["text"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取玩家信息详情 */
  getprofile: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["getProfile"];
        };
      };
    };
  };
  /**
   * 清空梦岛所有信息(服务器调用)
   * @description 需要删除的内容为：我的梦岛，心愿，留言板，曾用名（曾用名应该是游戏这边的）, token计算方式和 auth/login 相同
   */
  cleanAccount: {
    parameters: {
      query: {
        time: components["parameters"]["authTime"];
        account: components["parameters"]["authAccount"];
        urs: components["parameters"]["authUrs"];
        roleid: components["parameters"]["authRoleId"];
        token: components["parameters"]["authToken"];
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
  /** 同步玩家的背景图片(服务端) */
  backgroundSyncid: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["syncBackgroundReq"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["syncBackgroundRes"];
        };
      };
    };
  };
  /** 获取互动事件列表 */
  getevents: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        type?: components["parameters"]["eventType"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 添加互动事件 */
  addevent: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        server: components["parameters"]["server"];
        parameter?: components["parameters"]["parameter"];
        type?: components["parameters"]["eventType"];
        eventtoken?: components["parameters"]["eventtoken"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取地理位置(客户端家谱) */
  infoGetLocations: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetids?: components["parameters"]["targetids"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取玩家头像 */
  infoGetAvatar: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 附近的玩家 */
  locationNearbyPlayers: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        skey?: components["parameters"]["skey"];
        /**
         * @description 经度
         * @example 120.19175
         */
        longitude?: number;
        /**
         * @description 纬度
         * @example 30.187599
         */
        latitude?: number;
        /**
         * @description 附近的人距离(m)
         * @example 5000
         */
        distance?: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 大神web端登录 */
  authLoginByDs: {
    parameters: {
      query?: {
        /** @description 大神jssdk返回的token */
        accessToken?: string;
        /** @description 模拟登陆的roleId， 仅测试环境有效 */
        mockRoleId?: number;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取用户朋友圈或者指定用户的朋友圈 */
  listMoment: {
    parameters: {
      query?: {
        page?: components["parameters"]["page"];
        pagesize?: components["parameters"]["pagesize"];
      };
    };
    requestBody?: {
      content: {
        "application/json": components["schemas"]["MomentListRequestBody"];
      };
    };
    responses: {
      200: components["responses"]["MomentGetMomentsRes"];
    };
  };
  /**
   * 发表朋友圈长文
   * @deprecated
   */
  momentAddArticle: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        title?: components["parameters"]["m_title"];
        content?: components["parameters"]["m_content"];
        imglist?: components["parameters"]["imglist"];
        videolist?: components["parameters"]["videolist"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 动态-通过id获取动态详情 */
  getMomentById: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        textStyle: components["parameters"]["textStyle"];
      };
    };
    responses: {
      200: components["responses"]["MomentGetDetailRes"];
    };
  };
  /** 动态-查看本帮 */
  listMomentByGuild: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["MomentListByGuildReq"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 添加动态
   * @description 话题特殊文本格式b2例子 `<link button=coser活动,PSHotTalk,2>`
   * @玩家功能富文本格式例子  `<link button=太刀川庆,AtPlayer,281851200548>` 代表@281851200548这个玩家
   */
  addMoment: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["MomentAddRequestBody"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 转发动态 */
  momentForward: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        text: components["parameters"]["textRequired"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 删除动态 */
  delMoment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 热门动态-本服热门 */
  gethotmoments: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["MomentHotListRequestBody"];
      };
    };
    responses: {
      200: components["responses"]["MomentListHotRes"];
    };
  };
  /** 热门动态-全服热门 */
  getallhotmoments: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["MomentAllServerHotListRequestBody"];
      };
    };
    responses: {
      200: components["responses"]["MomentListHotRes"];
    };
  };
  /** 获取更多评论 */
  commentMore: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        comment_id: components["parameters"]["commentid2"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 评论列表 */
  commentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        momentId: components["parameters"]["momentId"];
        lastid?: components["parameters"]["lastid"];
        pagesize?: components["parameters"]["pagesize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 添加评论或回复 */
  addcomment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        text?: components["parameters"]["text"];
        replyid?: components["parameters"]["replyid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            code: number;
            /** @description 错误提示信息 */
            msg?: string;
            data?: {
              id?: number;
            };
          };
        };
      };
    };
  };
  /** 删除评论 */
  delcomment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["commentid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 点赞心情 */
  likemoment: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        id: components["parameters"]["momentid"];
        action?: components["parameters"]["likeAction"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 关注
   * @description |ErrorCode| Message|
   * |---| ---|
   * |-1000 | 到达关注上限|
   */
  followAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 取消关注 */
  followCancel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 取消关注 */
  followCancelBatch: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetids?: components["parameters"]["cacelTargetIds"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 关注列表 */
  followFollowList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 关注列表 */
  following: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 粉丝列表 */
  followers: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 梦岛个人信息左侧自动获取地理位置依赖的接口 */
  getlocation: {
    responses: {
      200: components["responses"]["GetLocationRes"];
    };
  };
  /** 根据ip查询ip库返回地理位置 */
  commonIp2location: {
    responses: {
      200: components["responses"]["GetLocationRes"];
    };
  };
  /** 获取nosToken */
  nosGettoken: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        type: components["parameters"]["nosType"];
        extname: components["parameters"]["nosExtName"];
        style: components["parameters"]["nosStyle"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 活动话题列表 */
  topicList: {
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["SuccRes"] & {
            data?: components["schemas"]["Topic"][];
          };
        };
      };
    };
  };
  /** 获取排行 */
  getrank: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        serverid: components["parameters"]["serverid"];
        /**
         * @description | 类型 |      value |
         * |----------|:-------------:|
         * | 人气 |  1|
         * | 鲜花 |  2|
         * | 收花 |  3|
         * | 送花 |  4|
         * | 人气周榜 |  5|
         * | 鲜花周榜|  6|
         * | 收花周榜 |  7|
         * | 送花周榜|  8|
         */
        type: 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 列出话题心情 */
  topicMomentList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        topicId?: number;
        sort?: "hot" | "new" | "image" | "follow";
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 添加话题 */
  topicAdminAdd: {
    requestBody?: {
      content: {
        "application/json": {
          /** @example #牛图PK大赛# */
          name?: string;
          /** @example http://hi-163-qnm.nosdn.127.net/upload/201902/27/4dce5c803a8211e9ba846b43c7ff77bb.jpg */
          banner?: string;
          /**
           * @description 对应话题对应调整的url
           * @example http://test.nie.163.com/test_html/qnm/dtds/#/center/
           */
          url?: string;
          /** @example 带话题#牛图PK大赛#发布梦岛，晒出你的牛图，有什么装备趣闻让大家开开眼！ */
          desc?: string;
          /**
           * @description 定时置顶的时间
           * @example 1605519946681
           */
          topAt?: number;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 删除话题 */
  topicAdminUpdateStatus: {
    parameters: {
      query?: {
        id?: number;
        /**
         * @description | value  | desc |
         * |-------|------|
         * | -1    | 下架 |
         * | 0     | 发布 |
         * | 0     | 置顶 |
         */
        status?: -1 | 0 | 1;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 话题列表 */
  topicAdminList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取nosToken(开放接口) */
  openGetnostoken: {
    parameters: {
      path: {
        /** @example md14e45ab34b */
        filename: string;
        extname: "jpg" | "jpeg" | "png";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取Nos上传Token */
  fuxiGetnostoken: {
    parameters: {
      query: {
        type: "photo_enhance";
        /** @example png */
        extName: "jpg" | "jpeg" | "png";
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 上传官网职业头像 */
  gmCmdJobAvatarUpload: {
    parameters: {
      query?: {
        /** @description 性别 */
        gender?: "male" | "female";
        /** @description 职业 */
        clazz?: components["schemas"]["Clazz"];
      };
    };
    requestBody?: {
      content: {
        "multipart/form-data": {
          /**
           * Format: binary
           * @description 职业头像图片
           */
          avatar?: string;
        };
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 新增心愿 */
  wishlistAdd: {
    requestBody?: {
      content: {
        "application/json": components["schemas"]["AddWishPayload"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 助力心愿单 */
  wishlistAddhelps: {
    requestBody?: {
      content: {
        /**
         * @example {
         *   "roleid": 100100001,
         *   "targetid": 100100001,
         *   "wishId": "9c3dbc74f33dd3f2",
         *   "text": "example text",
         *   "progress": 100,
         *   "history": [
         *     10,
         *     20,
         *     70
         *   ]
         * }
         */
        "application/json": Record<string, never>;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /**
   * 修改状态
   * @description status:
   *   ```
   *   enum EWishStatus {
   *       Default = 0, //默认，玩家可以助力、删除等操作
   *     Reward = 1, //已领奖
   *     Delete = 2, //已删除
   *     Return = 3, //已返还
   *   }
   *   ```
   * visibility:
   *   ```
   *   enum EVisibility {
   *       All = 0, //所有人可见
   *       None = 1, //无人可见
   *       Owner = 2  //自己可见
   *   }
   *   ```
   * status与isHide参数至少传递其中一个参数
   */
  wishlistUpdatestatus: {
    requestBody?: {
      content: {
        /**
         * @example {
         *   "roleid": 100100001,
         *   "wishId": "9c3dbc74f33dd3f2",
         *   "status": 0,
         *   "visibility": 0
         * }
         */
        "application/json": Record<string, never>;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 同步心愿单 */
  wishlistSyncone: {
    requestBody?: {
      content: {
        /**
         * @example {
         *   "roleid": 100100001,
         *   "wishlist": [
         *     {
         *       "roleid": 100100001,
         *       "wishId": "9c3dbc74f33dd3f2",
         *       "type": 1,
         *       "text": "example text",
         *       "templateId": 111,
         *       "regionId": 1,
         *       "num": 11,
         *       "totalProgress": 11,
         *       "startTime": 1599725110252,
         *       "endTime": 1599725110252,
         *       "status": 0,
         *       "helps": [
         *         {
         *           "roleid": 111011,
         *           "targetid": 100100001,
         *           "wishId": "9c3dbc74f33dd3f2",
         *           "progress": 100,
         *           "history": [
         *             10,
         *             20,
         *             70
         *           ]
         *         }
         *       ]
         *     }
         *   ]
         * }
         */
        "application/json": Record<string, never>;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取某人心愿单 */
  wishlistListbyrole: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 获取自己和朋友的心愿单 */
  wishlistList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 查看某条心愿单 */
  wishlistDetail: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wishId: components["parameters"]["wishId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 添加文本类心愿助力 */
  wishlistHelpTextAdd: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        wishId: components["parameters"]["wishId"];
        text: components["parameters"]["helpText"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 删除文本类心愿助力 */
  wishlistHelpTextDel: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        helpId: components["parameters"]["helpId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: never;
      };
    };
  };
  /** 更新指定位置的烟花图片 */
  fireworkPhotoUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        url: components["parameters"]["nosUrl"];
        index?: components["parameters"]["index"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /** @example 1 */
              id: number;
            };
          };
        };
      };
    };
  };
  /** 删除指定位置的图片 */
  fireworkPhotoRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        index?: components["parameters"]["index"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code?: number;
            data?: {
              /** @example 0 */
              id?: number;
            };
          };
        };
      };
    };
  };
  /** 玩家烟花图片列表(10张) */
  fireworkPhotoList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["FireworkPhotoListRes"];
        };
      };
    };
  };
  /** 通过图片id列表获取图片 */
  fireworkPhotoGetByIds: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 分享图片列表(csv格式, 最大四个) */
        ids?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["FireworkPhotoListRes"];
        };
      };
    };
  };
  /** 更新指定位置的家园自定义装饰图片 */
  homeDecoratePhotoUpdate: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        url: components["parameters"]["nosUrl"];
        index?: components["parameters"]["index"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /** @example 1 */
              id: number;
            };
          };
        };
      };
    };
  };
  /** 删除指定位置的图片 */
  homeDecoratePhotoRemove: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        index?: components["parameters"]["index"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code?: number;
            data?: {
              /** @example 0 */
              id?: number;
            };
          };
        };
      };
    };
  };
  /** 玩家庄园自定义图片列表(10张) */
  homeDecoratePhotoList: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        targetid?: components["parameters"]["targetid"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["FireworkPhotoListRes"];
        };
      };
    };
  };
  /** 通过图片id列表获取图片 */
  homeDecoratePhotoGetByIds: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        /** @description 分享图片列表(csv格式, 最大四个) */
        ids?: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": components["schemas"]["FireworkPhotoListRes"];
        };
      };
    };
  };
  /** 获取上传玩家日志文件NosToken */
  getReportLogNosToken: {
    parameters: {
      query: {
        objectname: string;
        device_id: components["parameters"]["deviceId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: components["schemas"]["NosToken"];
          };
        };
      };
    };
  };
  /** 添加设备汇报日志文件 */
  addReportLog: {
    parameters: {
      query: {
        device_id: components["parameters"]["deviceId"];
        url: string;
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /** @example 1 */
              id?: number;
            };
          };
        };
      };
    };
  };
  /** 玩家日志文件列表检索 */
  listReportLog: {
    parameters: {
      query: {
        /** @description 开始时间 单位(ms) */
        start_time?: number;
        /** @description 结束时间 单位(ms) */
        end_time?: number;
        device_id: components["parameters"]["deviceId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              list?: components["schemas"]["ReportLogItem"][];
              meta?: components["schemas"]["PaginationMeta"];
            };
          };
        };
      };
    };
  };
  /** 列出允许设备名单列表 */
  listAllowDevice: {
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              deviceIds?: string[];
            };
          };
        };
      };
    };
  };
  /** 添加设备到允许名单 */
  addAllowDevice: {
    parameters: {
      query: {
        device_id: components["parameters"]["deviceId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /** @example 1 */
              len?: number;
            };
          };
        };
      };
    };
  };
  /** 从允许名单中删除设备 */
  removeAllowDevice: {
    parameters: {
      query: {
        device_id: components["parameters"]["deviceId"];
      };
    };
    responses: {
      /** @description OK */
      200: {
        content: {
          "application/json": {
            /** @example 0 */
            code: number;
            data: {
              /** @example 1 */
              len?: number;
            };
          };
        };
      };
    };
  };
  /**
   * 动态抽奖-添加
   * @description 动态抽奖-添加, **游戏服务器调用**
   */
  serverMomentLotteryAdd: {
    requestBody: {
      content: {
        "application/json": components["schemas"]["ServerMomentLotteryAddReq"];
      };
    };
    responses: {
      200: components["responses"]["ServerMomentLotteryAddRes"];
    };
  };
  /**
   * 动态抽奖-展示
   * @description 动态抽奖-展示, 用于包含动态抽奖的动态展示当前的抽奖信息
   */
  momentLotteryShow: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        momentId: components["parameters"]["momentId"];
      };
    };
    responses: {
      200: components["responses"]["res-moment-lottery-show"];
    };
  };
  /** 动态抽奖-中奖玩家名单 */
  momentLotteryWinners: {
    parameters: {
      query: {
        roleid: components["parameters"]["roleid"];
        momentId: components["parameters"]["momentId"];
        page?: components["parameters"]["page"];
        pageSize?: components["parameters"]["pageSize"];
      };
    };
    responses: {
      200: components["responses"]["MomentLotteryWinnersRes"];
    };
  };
  /** 添加转服记录, 并完成所有RoleId相关数据的迁移 */
  serverTransferAdd: {
    parameters: {
      query: {
        /** @description 转服前的ID */
        oldId?: number;
        /** @description 转服后的ID */
        newId?: number;
        time: components["parameters"]["time"];
        nonce?: components["parameters"]["nonce"];
        /** @description token计算方式为 md5(ASCII升序参数的值 + salt),  即为 md5(newId + nonce + oldId + time + auth_slat) */
        token?: string;
      };
    };
    responses: {
      200: components["responses"]["ServerTransferAddRes"];
    };
  };
  /** filePicker文件审核回调 */
  serverFpFileReviewCallback: {
    requestBody: {
      content: {
        "application/json": {
          /**
           * @description 项目名
           * @example test
           */
          project_name: string;
          /**
           * @description 操作者
           * @example <EMAIL>
           */
          operator: string;
          files: {
              /**
               * @description 文件id
               * @example 625f7221173f910c0fdb544b0HfIAxYq02
               */
              file_id?: string;
              /**
               * @description 文件原始状态
               * @example 1
               */
              from_status?: number;
              /**
               * @description 审核后状态
               * @example 3
               */
              to_status?: number;
              /** @description 文件的extraInfo信息 */
              extra_info?: {
                /**
                 * @description 审核回调地址
                 * @example http://example.com/callback
                 */
                review_callback_url?: string;
              };
            }[];
        };
      };
    };
    responses: {
      200: components["responses"]["ApiActionRes"];
    };
  };
}
