"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.reqSlowLogCfg = exports.dbSlowLogCfg = exports.bunyanPoPoAlertCfg = exports.facePinchCfg = exports.homeDecoratePhotoCfg = exports.pageCfg = exports.searchCfg = exports.activityLoginCfg = exports.lbsCfg = exports.rankFetchCfg = exports.activityCookieCfg = exports.fakeImageAuditCfg = exports.chatPhotoCfg = exports.yunyingLog = exports.CardNotionMinLevel = exports.FriendCircleMinLevel = exports.PyqTopicMaxMomentSize = exports.watchQueueCfg = exports.logCfg = exports.smartMemorizeCfg = exports.cardNotionCfg = exports.zpxx2021Cfg = exports.shutDown = exports.redisCfg = exports.reportLogCfg = exports.activityAdminIpList = exports.activityTopicCfg = exports.photoViewCfg = exports.ndzj2020Cfg = exports.fireworkPhotoCfg = exports.WishListCfg = exports.gameServerIps = exports.Features = exports.gmCmdCfg = exports.askNingJingCfg = exports.TopicAdminOpenIds = exports.ActivityAdminOpenIds = exports.ActivityAdminUrs = exports.anniversarygift2020Cfg = exports.hotMomentCfg = exports.qyfhCfg = exports.cors = exports.MAX_UPLOAD_SIZE = exports.followCfg = exports.serverCfg = exports.paopaoCfg = exports.fpCfg = exports.equipRankCfg = exports.musicClubAuditAdminCfg = exports.testCfg = void 0;
exports.musicClubCfg = exports.gmWebCfg = exports.timezoneCfg = exports.informCfg = exports.easyMonitorCfg = exports.cronJobCfg = exports.momentTagCfg = exports.momentLotteryCfg = exports.fixMomentIdServerIdCfg = exports.apiAuthTokenCfg = exports.docCfg = exports.dailyLoginCfg = exports.serverListCfg = exports.envSdkApiCfg = exports.serverListUrl = exports.momentAtCfg = exports.fashionLotteryCfg = exports.kafkaCfg = exports.momentCfg = exports.concurrencyCfg = exports.netCookieCfg = exports.openIdCfg = exports.sendPicBufferCfg = void 0;
const globalConfig = require("../../common/config");
exports.testCfg = {
    test_env: false,
    req_log: false,
    skip_token: false,
    skip_openId_auth: false,
    skip_ip_auth: false,
    skip_skey_check: false,
    skip_token_check: false,
    /** 完全关闭检查, 只为单元测试使用 */
    skip_skey_check_full: false,
    fake_shut_down: false,
    source_map_support: false,
    token_time_diff_check: false,
    skip_audit: false,
};
// 音乐社团审核管理后台配置
exports.musicClubAuditAdminCfg = {
    enabled: process.env.MUSIC_CLUB_AUDIT_ADMIN_ENABLED === 'true',
};
exports.equipRankCfg = {
    tableName: "ods_la_bing_qi_pu_rank",
    cycleBegDate: "2018-05-21",
    cycleEndDate: "2018-06-20",
    version: "2021",
    commentInterval: 60, // 1分钟(单位s)
};
exports.fpCfg = {
    project: "l10-md-cn",
    secretKey: "iCW8ImdspRNk8IiLHoudxiJ2udjp4UPv",
    devMode: false,
    region: "MainLand",
    policy: {
        fsizeLimit: [0, 20 * 1024 * 1024],
        mimeLimit: ["image/*", "video/*", "audio/*"],
    },
    tokenExpire: 60,
    xFpToken: "bJ1sZBiChodimqDvVgvRjhPCeRdAb4NM",
};
exports.paopaoCfg = {
    url: "http://proxy.kong.svc/leihuo_ccc_l10_242",
    alarmReceivers: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
};
exports.serverCfg = {
    name: "l10_md_api",
    port: 3002,
    apiPrefix: "/qnm",
    region: "cn",
};
exports.followCfg = {
    fixedFollowLimit: 5000,
    followLimitDate: "2020-04-22 23:59:59",
};
exports.MAX_UPLOAD_SIZE = 5 * 1024 * 1024; // 5M
exports.cors = {
    origins: [
        /^https?:\/\/.*\.163\.com(:\d+)?/,
        /^https?:\/\/.*\.netease\.com(:\d+)?/,
        /^https?:\/\/localhost(:\d+)?/,
        /^https?:\/\/127.0.0.1(:\d+)?/,
    ],
};
exports.qyfhCfg = {
    enable: false,
    name: "qyfhguild2023",
    topicName: "风云帮会",
    attendMinImage: 2,
    // 官网同步的API接口配置
    activityApiHost: "http://file.mg.163.com",
    applyApiPath: "/public/share/common_activity_treeterminal/qnm/{{qyfhName}}/server/apply/chusai",
    renqiChangeApiPath: "/public/share/common_activity_treeterminal/qnm/{{qyfhName}}/202303/md/renqichange",
    skip_api_cache: true,
};
exports.hotMomentCfg = {
    hotSize: 400,
    minHot: 0,
    // 是否在更新热门同步的时候更新图库
    enableUpdateTuku: true,
    // 定时任务更新热度的延迟时间
    hotRefreshDelay: 50,
    // 动态的热度值衰减到1的秒数
    hotExpireMaxSeconds: 5 * 86400,
    // 非标签相关热门动态的热度值衰减到1的秒数, 默认为1天
    hotExpireMaxSecondsForNoTag: 86400,
};
exports.anniversarygift2020Cfg = {
    name: "anniversarygift2020",
    applyApi: "https://ssl.hi.163.com/file_mg/public/qnm/wemedia_submit/push/data",
    dateRange: { startDate: "2020-05-07 10:00", endDate: "2020-05-22 23:59" },
    topicNames: ["4年对比挑战", "为4周年庆生", "#结缘故事大赛#"],
    minImgLimit: 1,
};
exports.ActivityAdminUrs = ["<EMAIL>", "<EMAIL>"];
exports.ActivityAdminOpenIds = ["hzlimin1", "hzwangzhenhua", "hzwangyue5", "huangxia"];
exports.TopicAdminOpenIds = [
    "hzwangzhenhua",
    "hzlimin1",
    "yufeifan01",
    "hzyangchenlu1",
    "hzmengyawen",
    "wb.lipei01",
    "hzwangyue5",
    "huangxia",
    "zhangqi07",
    "lilinghan",
];
exports.askNingJingCfg = {
    dateRange: { startDate: "2020-06-22 00:00", endDate: "2020-06-26 19:00" },
    name: "askNingJing",
    applyApi: "http://file.mg.163.com/public/qnm/ningjing2020/mdtalk/apply",
    topicName: "静姐看看我",
};
exports.gmCmdCfg = {
    enable: true,
    gms: ["hzwangzhenhua"],
};
exports.Features = {
    wishList: true,
    fireworkPhoto: true,
    background: true,
    reportLog: true,
    filepick: true,
    slientLove: true,
    cardComment: true,
    complain: true,
    equipRank: true,
    thisDayThatYear: true,
    momentDelNosFile: false,
    metricPlugin: true,
    showDoc: false,
    facePinch: true,
    homeDecoratePhoto: true,
    topicAdmin: true,
    fashionLottery: true,
    dailyLogin: true,
    /** 是否开启kafka相关功能 */
    kafka: false,
};
exports.gameServerIps = ["***********", "************", "**************"];
exports.WishListCfg = {
    IpLimit: exports.gameServerIps,
    maxHelpTextListSize: 10,
    maxHelpCount: 10,
    momentTextTemplate: "我许下了一个小小心愿：%s<link button=点击助力,WishDetail,%s>",
    momentTextWithOutRewardTemplate: "我许下了一个小小心愿: %s<link button=点击查看,WishDetail,%s>",
};
exports.fireworkPhotoCfg = {
    maxSize: 10,
};
exports.ndzj2020Cfg = {
    activityName: "ndzj2020_wish",
    momentTextTemplate: "2021年了, 希望我的新年心愿能够实现<link button=点击助力,WishDetail,%s> %s",
    topicName: "# 2021新年心愿#",
    helpRankKey: "l10:wish_list:ndzj2020:rank",
    helpRankExpireTime: 6 * 30 * 24 * 3600,
    rankSize: 3,
    recentHelpSize: 10,
    startDate: "2020-01-01 00:00:00",
    endDate: "2020-12-31 23:59:59",
};
exports.photoViewCfg = {
    NOS_REJECT_SELF_VIEW_URL: "http://hi-163-common.nosdn.127.net/common/deny.jpg",
    NOS_AUDITING_PUBLIC_URL: "https://hi-163-common.nosdn.127.net/upload/201705/05/e1030d90315e11e7956f95595545646a",
    NOS_WATERMARK_URL: "https://hi-163-qnm.nosdn.127.net/common/watermark-audit.png",
    NOS_AUDITING_WATERMARK_SUFFIX: "watermark&type=1&gravity=center&image=Y29tbW9uL3dhdGVybWFyay1hdWRpdC5wbmc=",
    NOS_THUMB_SUFFIX: "imageView&thumbnail=250y160",
    NOS_VIDEO_COVER_SUFFIX: "vframe=1",
    FP_REJECT_SELF_VIEW_URL: "https://l10hmt-md.fp.ps.easebar.com/file/5f89671d1b74123a22c2cbcciu6WQo8a01",
    FP_AUDITING_PUBLIC_URL: "https://l10hmt-md.fp.ps.easebar.com/file/5f99259c1b74123befc242abrwKbftt101",
    FP_WATERMARK_URL: "https://l10hmt-md.fp.ps.easebar.com/file/5f8967aa1901a83b8b9c0b1dgcHHSGhc01",
    FP_AUDITING_WATERMARK_SUFFIX: "watermark/1/image/aHR0cHM6Ly9sMTBobXQtbWQuZnAucHMuZWFzZWJhci5jb20vZmlsZS81Zjg5NjdhYTE5MDFhODNiOGI5YzBiMWRnY0hIU0doYzAx/gravity/5",
    FP_THUMB_SUFFIX: "imageView/0/l/250/s/160",
    VIDEO_COVER_FRAME: 1,
    FP_VIDEO_COVER_SUFFIX: "vframe/w/250/h/160",
};
exports.activityTopicCfg = {
    maxSize: 4,
};
exports.activityAdminIpList = ["**************", "**************", "**************"];
exports.reportLogCfg = {
    objectPrefix: "report_log/",
    allowDevieKey: "report_log_allow_device_ids",
};
let redisConfig = {
    hosts: ["127.0.0.1"],
    host: "127.0.0.1",
    port: 6379,
    db: 0,
    password: "opensesame",
    no_ready_check: true,
    prefix: "l10_md:",
};
if (globalConfig.REDIS_CFG) {
    // use global redis cfg
    const globalRedisCfg = globalConfig.REDIS_CFG;
    if (globalRedisCfg.hosts) {
        redisConfig.hosts = globalRedisCfg.hosts;
    }
    redisConfig.host = globalRedisCfg.host;
    redisConfig.port = globalRedisCfg.port;
    redisConfig.password = globalRedisCfg.password;
    redisConfig.no_ready_check = globalRedisCfg.no_ready_check;
}
exports.redisCfg = redisConfig;
exports.shutDown = false;
exports.zpxx2021Cfg = {
    name: "zpxx2021",
    topicName: "仙宗风华",
    attendMinImage: 2,
    applyApi: "http://file.mg.163.com/public/share/common_activity_treeterminal/qnm/zpxx2021/server/apply/chusai",
    fusaiRoleIdApi: "http://file.mg.163.com/public/share/common_activity_treeterminal/qnm/zpxx2021/202106/fusaidata",
    renqiChangeApi: "http://file.mg.163.com/public/share/common_activity_treeterminal/qnm/zpxx2021/202106/md/renqichange",
    renqiStartDate: "2021-06-25 10:00",
    timeRange: {
        chusai: { startDate: "2021-06-24 10:00", endDate: "2021-07-04 23:59" },
        fusai: { startDate: "2021-07-06 10:00", endDate: "2021-07-14 23:59" },
    },
    skip_api_cache: true,
};
exports.cardNotionCfg = {
    openMinLevel: 8,
};
exports.smartMemorizeCfg = {
    disableCache: false,
};
exports.logCfg = {
    schema: "com.netease.leihuo.ccc.base.model.tables.v1.L10MdLog",
    dir: "/srv/logs",
    level: "info",
    env: "prod",
    yunyingdir: "/yunying",
    prefix: "",
    printInConsole: false,
};
exports.watchQueueCfg = {
    alarmSize: 150000,
};
/** pyq_topic表记录最近动态列表数 */
exports.PyqTopicMaxMomentSize = 100;
exports.FriendCircleMinLevel = 30;
exports.CardNotionMinLevel = 8;
exports.yunyingLog = {
    dir: "",
    env: "",
    level: "info",
    prefix: "l10",
    printInConsole: false,
    unlinkIntervalDays: 14,
};
exports.chatPhotoCfg = {
    /** 严格审核模式， 此模式下需要先审核通过才显示图片url */
    strictAudit: {
        enable: true,
        startDate: "2022-03-04 00:00:00",
        endDate: "2022-03-11 23:59:59",
    },
    chatPhotoExpireTime: 5 * 60,
    chatPhotoSizeExpireTime: 24 * 3600,
    divisionLevel: 109,
    divisionVip: 6,
};
exports.fakeImageAuditCfg = {
    enable: false,
    delay: 15000,
    theme: "fakeImageAudit",
};
exports.activityCookieCfg = {
    path: "/",
    domain: ".163.com",
    httpOnly: true,
    sameSite: "none",
    secure: true,
};
/** getrank 排行接口启用应用内过滤模式 */
exports.rankFetchCfg = {
    filterDupRoleId: true,
    // 获取数据放大倍率
    expandRatio: 1.5,
};
exports.lbsCfg = {
    // disable nearby feature
    openNearby: false,
};
exports.activityLoginCfg = {
    useAccessTokenInUrl: false,
};
exports.searchCfg = {
    maxScanLimit: 50000,
};
exports.pageCfg = {
    defaultPageSize: 1,
};
exports.homeDecoratePhotoCfg = {
    maxSize: 10,
};
exports.facePinchCfg = {
    userCacheTime: 5 * 60,
    maxCollectPerRoleId: 100,
    maxUploadPerRoleId: 100,
    cloudAuthLoginSalt: "N1gEwZK7@Cy&4$RV#%8DaC6mNrUhm1#6",
    mimeLimit: ["image/*", "application/*", "video/*"],
    fsizeLimit: [0, 25 * 1024 * 1024],
    fpLimitRate: {
        seconds: 300,
        max: 10,
    },
    /** 大神导入接口需要配置ip白名单 */
    dashenIpAllowList: ["*************", "*************", "**************"],
    /** 部位功能上线时间，用于区分历史数据 */
    partsFeatureLaunchTime: '2024-01-01T00:00:00+08:00',
    /** 收藏和我的设计的总存储上限 */
    maxStorageCount: 300,
};
/** 日志报警渠道, 会推送到报警群 */
exports.bunyanPoPoAlertCfg = {
    enable: true,
    level: "error",
    webhookUrl: "https://lhpp-popo-server.apps-hp.danlu.netease.com/popo/popo/msg/send",
    secretKey: "lOMvNtTgjrT7WJrmXOknYA==",
    project: "qnm",
    biz: "md",
    env: "release",
    /** 最小推送间隔, 默认1分钟允许1次 */
    minNotifyInterval: 60,
    atUids: ["<EMAIL>"],
};
exports.dbSlowLogCfg = {
    enable: true,
    threshold: 500,
};
exports.reqSlowLogCfg = {
    enable: true,
    threshold: 1000,
};
exports.sendPicBufferCfg = {
    enable: true,
    buffersize: 50,
    syncInterval: 1000,
};
exports.openIdCfg = {
    mode: "easy",
    secret: "x2g&^JtBd@l2`1",
    infoKey: "common_auth_corp_info",
    tokenKey: "common_auth_corp_token",
    clientId: "04bd77de9b3711eb82fe246e965dfd84",
    clientSecret: "6ee6a2035d00457fb1954516e4eeafcd04bd7c2a9b3711eb82fe246e965dfd84",
    redirectUri: "",
};
exports.netCookieCfg = {
    path: "/",
    domain: ".netease.com",
    httpOnly: true,
};
exports.concurrencyCfg = {
    defaultConcurrency: 5,
};
exports.momentCfg = {
    cacheCommentsSize: 10,
    cacheCommentsExpireTime: 300,
};
exports.kafkaCfg = {
    clientId: "l10_ccc_md",
    brokers: [],
    topic: "",
    logLevel: "INFO",
    fromBeginning: false,
    partitionsConsumedConcurrently: 3,
    groupId: "l10_ccc_md_release",
    sasl: {
        mechanism: "plain",
        username: "",
        password: "",
    },
};
exports.fashionLotteryCfg = {
    enable: true,
    logConsumer: {
        clientId: "l10_ccc_md",
        groupId: "1l0_ccc_md_fashion_lottery",
        fromBeginning: false,
    },
    // 数据保留3个月才能被查询
    keepMonth: 3,
};
exports.momentAtCfg = {
    // 每个动态最大at玩家数量
    maxAtNum: 5,
    // searchHitMaxSize
    searchHitMaxSize: 3,
};
exports.serverListUrl = "https://s3.cn-north-1.amazonaws.com.cn/l10-public/serverlists/online-with-date.txt";
// export let serverListUrl = "https://l10vn-serverlist.tms.easebar.com/hostlists/online-with-date.txt";
exports.envSdkApiCfg = {
    host: "http://qnm-envsdk-service-release.apps-hp.danlu.netease.com",
    /** 文本审核时间 */
    timeout: 2000,
};
exports.serverListCfg = {
    extraServerIds: [],
    // 新增服务器id范围认为是官服, enable为true时生效, 用于测试环境的服务器id不在服务器列表需要识别为官服的处理
    extraOfficialServer: {
        enable: false,
        minServerId: 1,
        maxServerId: 20,
    },
    // 内网服务器id范围
    innerServerIds: {
        enable: false,
        minServerId: 1,
        maxServerId: 20,
    },
};
exports.dailyLoginCfg = {
    // 查询每日登录允许的最大时间间隔
    keepDays: 7,
    // 用于查询接口授权
    secretKey: "bGkrHN5NuuKUg2JpeEgdrw7a",
};
exports.docCfg = {
    enable: false,
    mountPath: "/docs/swagger",
    title: "倩女手游梦岛接口",
    docUrl: "http://localhost:9992/qnm/doc/swagger/swagger.yaml",
};
// api授权配置
exports.apiAuthTokenCfg = {
    // 家园模板服务授权
    defaultExpire: 5 * 60,
    authApps: [
        {
            // 授权appId
            appId: "qnm-home-template",
            // 授权app secret
            secret: "E7723C87DDBA7E54C3D41649CABE2",
            // 授权token 有效期
            expireSeconds: 5 * 60,
        },
        {
            // 授权appId
            appId: "qnm_yeardata_2024",
            // 授权app secret
            secret: "5CA24F66ADBE6FB9791D2B18D7B9B",
            // 授权token 有效期
            expireSeconds: 5 * 60,
        },
    ],
};
exports.fixMomentIdServerIdCfg = {
    // 是否开启自动修复
    enable: true,
    // 扫描开始时间
    scanBegTime: "2024-12-19T12:04:51+08:00",
    // 扫描批次大小
    scanBatchSize: 100,
};
exports.momentLotteryCfg = {
    // 游戏服务器和当前服务器允许的最大偏移时间
    gameTimeOffsetAllowMaxSeconds: 10 * 60,
    // 阳光普照抽奖最大中奖人数
    luckShineAllMaxWinnerNum: 100,
    // 单份奖品最多种类
    prizeCategoryMaxNum: 9,
    // 单份奖品每种种类最多数量
    prizeItemMaxNumPerCategory: 999,
    // 开奖时间选择最短时长
    drawTimeMinDurationHours: 24,
    // 开奖时间选择最大时长, 最多30天
    drawTimeMaxDurationHours: 24 * 30,
    // 动态抽奖公示期时间, 单位s
    lotteryGongShiSeconds: 3600,
    // 抽奖接口验证token salt, 用于加密token
    authTokenSalt: "&7%^!*(_)$#@!~_$%^#+#",
    // 是否跳过抽奖接口token验证
    skipAuthTokenCheck: false,
    // 奖品总价值最低100灵玉
    lotteryMinJade: 100,
    // 发布抽奖动态的并发锁过期时间
    addLotteryLockTTLMs: 10 * 60 * 1000,
    // 动态抽奖信息缓存时间
    lotteryInfoCacheSeconds: 10 * 60,
    // 参与抽奖的最低信用分
    attendLotteryMinCreditScore: 550,
    // 每个玩家每天最低参与抽奖的次数, 默认参与最先满足条件的前3次
    attendLotteryPerPlayerPerDayMaxTimes: 3,
    // 抽奖动态的sn最大长度
    lotterySnMaxLength: 24,
    // 默认关闭官服玩家发起抽奖的检查, 游戏服务器会检查是否官服
    officialServerLotteryCheck: false,
    //游戏抽奖接口地址配置  http://*************/gms/postReceiveLimit.php, 这个是丹炉的内网代理
    // gmsPostReceiveLimitApiUrl: "http://apps-sl.danlu.netease.com:37975/qnm/gms/postReceiveLimit.php",
    gmsPostReceiveLimitApiUrl: "http://apps-sl.danlu.netease.com:37975/qnm/gms/postReceiveLimit.php",
    // 游戏抽奖接口地址的服务器id参数，默认为"", 代表需要用oleId结合服务器列表计算服务器id
    // 可人工指定服务器id，用于内网环境指定服务器id验证流程
    gmsPostReceiveLimitApiServerId: "",
    // 游戏抽奖接口mock配置
    gmsPostReceiveLimitApiMock: {
        // 是否开启mock
        enable: false,
        // 用来模拟抽奖接口返回的结果
        mockApiRes: '{"status":"OK"}',
    },
    // 游戏抽奖接口超时时间 ms
    gmsPostReceiveLimitApiTimeout: 5000,
    // 开奖符合要求的最小人数
    minLotteryCandidateNum: 10,
    // 定时任务触发自动开奖, 每5秒检查1次
    drawCheckerCron: "*/5 * * * * *",
    // 开奖接口失败的时候最大重试次数
    maxRetryCount: 50,
    // 开奖接口失败的时候重试的延迟时间，单位毫秒, 默认10s
    retryDelayMsBase: 10 * 1000,
    // 开奖接口失败的时候重试的延迟时间最大值，单位毫秒, 默认10分钟
    retryDelayMsMax: 10 * 60 * 1000,
};
// 话题标签系统相关配置
exports.momentTagCfg = {
    // 动态允许的最大标签数量
    maxTagNumPerMoment: 5,
    // 抽奖动态允许的最大标签数量
    maxTagNumPerLotteryMoment: 6,
};
exports.cronJobCfg = {
    // 是否在cron job中嵌入健康检查服务, 默认开启, 方便容器环境监控
    embedHealthCheckServer: false
};
exports.easyMonitorCfg = {
    enable: false,
    xtransit: {
        server: `ws://xtransit-server.apps-hp.danlu.netease.com`,
        appId: 14,
        appSecret: "354ec3eac11688fe9032cf126dde42e6", // 创建应用得到的应用 Secret
    },
    logDir: '/tmp',
};
exports.informCfg = {
    // 未读消息接口最大数量, 保持旧接口兼容
    maxUnreadInformsPageSize: 50,
};
// 添加时区配置
exports.timezoneCfg = {
    cn: "+08:00",
    hmt: "+08:00",
    vn: "+07:00" // 越南服
};
// 一些Gm Web工具
exports.gmWebCfg = {
    // 是否开启GM Web
    enable: false
};
// 乐团系统相关配置
exports.musicClubCfg = {
    // 是否开启乐团系统
    enable: false,
    // 乐团总热度榜单数量
    allHotRankListSize: 50,
    // 乐团总热度榜单数量(服务端调用), 用的同一个cache数据源，，需要比allHotRankListSize小
    allHotRankListSizeForServer: 10,
    // 乐团新晋榜数量
    newHotRankListSize: 20,
    // 乐团新晋榜数量(服务端调用), 用的同一个cache数据源，，需要比newHotRankListSize小
    newHotRankListSizeForServer: 10,
    // 乐团模糊搜索最大扫码范围
    fuzzySearchMaxScanLimit: 10000,
    // 乐团排名最大显示数量
    maxRankToShow: 500,
    // 热度排行榜缓存过期时间(秒)
    hotRankCacheExpire: 1200,
    // 是否开启热度排行榜缓存刷新
    enableHotRankCacheRefresh: true,
    // 定时刷新热度排行榜缓存的cron表达式
    refreshRankCacheCron: '*/10 * * * *',
    // CC音频审核相关配置
    ccAudioAudit: {
        // CC 审核接口地址
        apiUrl: "http://inneraudioms.cc.163.com/inner/v1/audiorectranslatorext/custom_audio_job/create",
        // CC 应用ID
        appId: 10015,
        // 默认频道类型
        defaultChannelType: "bandclub",
        // 请求超时时间（毫秒）
        requestTimeout: 10000,
    },
    // 音频审核任务相关配置
    auditTimeoutMinutes: 30,
    auditMaxRetries: 3, // 最大重试次数
};
//# sourceMappingURL=config.all.js.map