"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkService = void 0;
const _ = require("lodash");
const errorCode_1 = require("../errorCode");
const apply_1 = require("../models/apply");
const collect_1 = require("../models/collect");
const like_1 = require("../models/like");
const work_1 = require("../models/work");
const facePartFilterService_1 = require("../services/facePartFilterService");
const partsUtils_1 = require("../utils/partsUtils");
class WorkService {
    static getPublicListByNew(params) {
        return __awaiter(this, void 0, void 0, function* () {
            const userId = params.curUser.id;
            // 验证部位筛选参数
            const validation = facePartFilterService_1.FacePartFilterService.validateFilterParts(params.filterParts);
            if (!validation.valid) {
                throw errorCode_1.ApiErrors.InvalidPartsSelection;
            }
            let query = work_1.WorkModel.publicScope();
            query = work_1.WorkModel.bodyShapeScope(query, params);
            // 应用部位筛选
            query = facePartFilterService_1.FacePartFilterService.buildFilterQuery(query, params.filterParts);
            const rows = yield work_1.WorkModel.powerQuery({
                initQuery: query,
                orderBy: [["id"], ["desc"]],
                pagination: { page: params.page, pageSize: params.pageSize },
            });
            const list = yield WorkService.formatListRes(rows, userId);
            const data = { list };
            return data;
        });
    }
    static checkExist(workId, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const r = yield work_1.WorkModel.findOne(Object.assign({ id: workId }, work_1.WorkModel.normalCondition()));
            if (!r) {
                throw errorCode_1.ApiErrors.WorkNotFound;
            }
            else {
                const isPublicAndAuditPass = r.auditStatus === 1 /* PASS */ && r.visibility === 0 /* Public */;
                const isSelfView = r.userId === userId;
                if (isPublicAndAuditPass || isSelfView) {
                    return r;
                }
                else {
                    throw errorCode_1.ApiErrors.WorkNotFound;
                }
            }
        });
    }
    static checkForImport(id) {
        return __awaiter(this, void 0, void 0, function* () {
            const r = yield work_1.WorkModel.findOne(work_1.WorkModel.importCondition(id));
            if (!r) {
                throw errorCode_1.ApiErrors.WorkNotFound;
            }
            else {
                return r;
            }
        });
    }
    static checkPublic(moment) {
        return __awaiter(this, void 0, void 0, function* () {
            if (moment.visibility !== 0 /* Public */) {
                throw errorCode_1.ApiErrors.WorkNotPublic;
            }
        });
    }
    static formatListRes(rows, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return WorkService.formatWorkRecordList(rows, userId);
        });
    }
    static formatWorkRecordList(rows, userId) {
        return __awaiter(this, void 0, void 0, function* () {
            if (_.isEmpty(rows))
                return [];
            const workIds = rows.map((r) => r.id);
            const likeWorkIds = yield like_1.LikeModel.filterUserLiked(userId, workIds);
            const collectWorkIds = yield collect_1.CollectModel.filterUserCollected(userId, workIds);
            const applyWorkIds = yield apply_1.ApplyModel.filterUserApplied(userId, workIds);
            const list = rows.map((r) => {
                const isLiked = _.includes(likeWorkIds, r.id);
                const isCollected = _.includes(collectWorkIds, r.id);
                const onceApplied = _.includes(applyWorkIds, r.id);
                const item = WorkService.formatWorkRecord(r, { isLiked, isCollected, onceApplied });
                return item;
            });
            return list;
        });
    }
    static formatWorkRecord(r, actionFlag) {
        // 计算可用的应用部位（考虑历史数据限制）
        const availableApplyParts = (0, partsUtils_1.getAvailableApplyParts)(r.allowedParts, r.createTime);
        const data = Object.assign({ id: r.id, userId: r.userId, roleId: r.roleId, roleName: r.roleName, shareId: r.shareId, jobId: r.jobId, gender: r.gender, createTime: r.createTime, likeCount: r.likeCount, collectCount: r.collectCount, applyCount: r.applyCount, hot: r.hot, title: r.title, desc: r.desc, dataUrl: r.dataUrl, image: r.image, visibility: r.visibility, allowedParts: r.allowedParts, availableApplyParts: availableApplyParts }, actionFlag);
        return data;
    }
}
exports.WorkService = WorkService;
//# sourceMappingURL=service.js.map