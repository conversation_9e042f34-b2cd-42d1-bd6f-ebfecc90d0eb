"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FACE_PARTS_NAMES = exports.FACE_PARTS = exports.ONE_DAY_SECONDS = exports.GUEST_MODE_ALLOW_API_SET = exports.MD_LOGIN_ROLE_ID_TYPE = exports.FACE_PINCH_CLOUD_AUTH_LOGIN_TYPE = void 0;
const constants_1 = require("../../constants");
// 游客模式登录
exports.FACE_PINCH_CLOUD_AUTH_LOGIN_TYPE = "face_pinch_cloud_account";
// 梦岛登录使用的idType
exports.MD_LOGIN_ROLE_ID_TYPE = "role";
exports.GUEST_MODE_ALLOW_API_SET = new Set([
    constants_1.apiPrefix + "/face_pinch/get_fp_token",
    constants_1.apiPrefix + "/face_pinch/work/add",
    constants_1.apiPrefix + "/face_pinch/work/get_by_share_id",
]);
exports.ONE_DAY_SECONDS = 86400; // 1天，单位秒
/** 脸部部位定义，使用位运算进行组合 */
exports.FACE_PARTS = {
    /** 塑形 */
    SHAPE: 1,
    /** 妆容 */
    MAKEUP: 2,
    /** 全脸 */
    FULL_FACE: 4,
};
/** 脸部部位名称映射 */
exports.FACE_PARTS_NAMES = {
    [exports.FACE_PARTS.SHAPE]: '塑形',
    [exports.FACE_PARTS.MAKEUP]: '妆容',
    [exports.FACE_PARTS.FULL_FACE]: '全脸',
};
//# sourceMappingURL=constant.js.map