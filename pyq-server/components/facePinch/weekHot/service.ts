import { format, startOfWeek, subDays } from "date-fns"
import { WeekHotModel } from "../models/weekHot"
import { WorkModel } from "../models/work"
import { FacePinchReq, FacePinchRes } from "../type"
import { WorkService } from "../work/service"
import { FacePinchCurUser } from "../userType"
import { FacePartFilterService } from "../services/facePartFilterService"
import { ApiErrors } from "../errorCode"

export class WeekHotService {
  static async getPublicList(
    params: FacePinchReq.WorkListPublic & FacePinchCurUser
  ): Promise<FacePinchRes.WorkListPublic> {
    const userId = params.curUser.id

    // 验证部位筛选参数
    const validation = FacePartFilterService.validateFilterParts(params.filterParts);
    if (!validation.valid) {
      throw ApiErrors.InvalidPartsSelection;
    }

    const curDate =  params.date ? new Date(params.date) : new Date()
    const lastWeekToday = subDays(curDate, 7)
    const ids = await this.getHotIds(lastWeekToday, params)
    const rows = await WorkModel.getPublicByIds(ids)
    const list = await WorkService.formatListRes(rows, userId)
    const data: FacePinchRes.WorkListPublic = { list }
    return data
  }

  static weekDateStr(date: Date | number) {
    const week = format(startOfWeek(date, { weekStartsOn: 1 }), "yyyy-MM-dd")
    return week
  }

  static async getHotIds(date: Date, params: FacePinchReq.WorkListPublic) {
    let query = WeekHotModel.scope().where("week", this.weekDateStr(date))
    query = WorkModel.bodyShapeScope(query, params)

    // 应用部位筛选 - 需要通过JOIN到work表来筛选allowedParts字段
    if (params.filterParts && FacePartFilterService.validateFilterParts(params.filterParts).valid) {
      const validAllowedParts = FacePartFilterService.getValidAllowedPartsValues(params.filterParts);
      query = query.join('pyq_face_pinch_work as work', 'pyq_face_pinch_week_hot.workId', 'work.id')
                   .whereIn('work.allowedParts', validAllowedParts);
    }

    const rows = await WeekHotModel.powerQuery({
      initQuery: query,
      select: ["workId"],
      orderBy: [
        ["hot", "id"],
        ["desc", "desc"],
      ],
      pagination: { page: params.page, pageSize: params.pageSize },
    })
    return rows.map((r) => r.workId)
  }
}