"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReqSchemas = void 0;
exports.ReqSchemas = {
    FacePinchWebPublicShareInfo: {
        type: "object",
        properties: {
            shareId: { type: "string" },
        },
        required: ["shareId"],
    },
    FacePinchDashenShareInfoImport: {
        type: "object",
        properties: {
            shareId: { type: "string" },
            roleId: { type: "number" },
            server: { type: "number" },
        },
        required: ["shareId", "roleId", "server"],
    },
    FacePinchWebPublicShareVideoInfo: {
        type: "object",
        properties: {
            shareId: { type: "string" },
        },
        required: ["shareId"],
    },
    FacePinchShareVideoAdd: {
        type: "object",
        properties: {
            video: { type: "string" },
        },
        required: ["video"],
    },
    FacePinchCloudGameAuthLogin: {
        type: "object",
        properties: {
            roleId: { type: "number" },
            account: { type: "string" },
            time: { type: "number" },
            token: { type: "string" },
        },
        required: ["account", "time", "token"],
    },
    FacePinchWorkListPublic: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            skey: { type: "string" },
            gender: { type: "number" },
            bodyShape: { type: "number", enum: [0, 1, 2, 3] },
            jobId: { type: "number" },
            page: { type: "number", default: 1 },
            pageSize: { type: "number", default: 10 },
            filterParts: { type: "number", minimum: 1, maximum: 7 },
        },
        required: ["roleid", "skey"],
    },
    FacePinchWorkListCollect: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            skey: { type: "string" },
            page: { type: "number", default: 1 },
            pageSize: { type: "number", default: 10 },
            filterParts: { type: "number", minimum: 1, maximum: 7 },
        },
        required: ["roleid", "skey"],
    },
    FacePinchWorkListSelf: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            skey: { type: "string" },
            page: { type: "number", default: 1 },
            pageSize: { type: "number", default: 10 },
            filterParts: { type: "number", minimum: 1, maximum: 7 },
        },
        required: ["roleid", "skey"],
    },
    FacePinchWorkGetByShareId: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, shareId: { type: "string" } },
        required: ["roleid", "skey", "shareId"],
    },
    FacePinchWorkDetail: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
        required: ["roleid", "skey", "id"],
    },
    FacePinchWorkSearch: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            skey: { type: "string" },
            kw: { type: "string", maxLength: 7, minLength: 2 },
            gender: { type: "number" },
            jobId: { type: "number" },
            bodyShape: { type: "number", enum: [0, 1, 2, 3] },
        },
        required: ["roleid", "skey", "kw"],
    },
    FacePinchWorkAdd: {
        type: "object",
        properties: {
            roleid: { type: "number" },
            skey: { type: "string" },
            dataUrl: { type: "string" },
            image: { type: "string" },
            title: { type: "string" },
            desc: { type: "string" },
            roleName: { type: "string" },
            gender: { type: "number" },
            visibility: { type: "number", enum: [0, 1] },
            bodyShape: { type: "number", enum: [0, 1, 2, 3] },
            jobId: { type: "number" },
            allowedParts: { type: "number", minimum: 1, maximum: 7, default: 7 },
        },
        required: ["roleid", "skey", "dataUrl", "image", "title", "roleName", "gender", "jobId", "allowedParts"],
    },
    FacePinchWorkUpdateVisibility: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" }, visibility: { type: "number", enum: [0, 1] } },
        required: ["roleid", "id", "visibility"],
    },
    FacePinchGetFpToken: {
        type: "object",
        properties: { roleid: { type: "number" } },
        required: ["roleid"],
    },
    FacePinchWorkDel: {
        type: "object",
        properties: { roleid: { type: "number" }, id: { type: "number" } },
        required: ["roleid", "id"],
    },
    FacePinchWorkApply: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
        required: ["roleid", "skey", "id"],
    },
    FacePinchWorkLike: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
        required: ["roleid", "skey", "id"],
    },
    FacePinchWorkCancelLike: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
        required: ["roleid", "skey", "id"],
    },
    FacePinchWorkCollect: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
        required: ["roleid", "skey", "id"],
    },
    FacePinchWorkCancelCollect: {
        type: "object",
        properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
        required: ["roleid", "skey", "id"],
    },
};
//# sourceMappingURL=type.js.map