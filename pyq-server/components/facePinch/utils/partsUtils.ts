import { FACE_PARTS, FACE_PARTS_NAMES } from '../constant';
import { facePinchCfg } from '../../../common/config';

/**
 * 验证部位选择的有效性
 * @param allowedParts 允许的部位值
 * @returns 是否有效
 */
export function validatePartsSelection(allowedParts: number): boolean {
  // 必须大于0且不能超过所有部位的组合值(7)
  return allowedParts > 0 && allowedParts <= (FACE_PARTS.SHAPE | FACE_PARTS.MAKEUP | FACE_PARTS.FULL_FACE);
}

/**
 * 获取部位的显示名称
 * @param parts 部位值
 * @returns 部位名称数组
 */
export function getPartsDisplayNames(parts: number): ("塑形" | "妆容" | "全脸")[] {
  const names: ("塑形" | "妆容" | "全脸")[] = [];
  if (parts & FACE_PARTS.SHAPE) names.push(FACE_PARTS_NAMES[FACE_PARTS.SHAPE]);
  if (parts & FACE_PARTS.MAKEUP) names.push(FACE_PARTS_NAMES[FACE_PARTS.MAKEUP]);
  if (parts & FACE_PARTS.FULL_FACE) names.push(FACE_PARTS_NAMES[FACE_PARTS.FULL_FACE]);
  return names;
}

/**
 * 判断是否为历史数据（在部位功能上线前创建）
 * @param createTime 创建时间戳（毫秒）
 * @returns 是否为历史数据
 */
export function isLegacyData(createTime: number): boolean {
  const launchTime = new Date(facePinchCfg.partsFeatureLaunchTime).getTime();
  return createTime < launchTime;
}

/**
 * 计算作品的可用应用部位（考虑历史数据限制）
 * @param allowedParts 作品允许的部位
 * @param createTime 作品创建时间
 * @returns 可用部位名称数组
 */
export function getAvailableApplyParts(allowedParts: number, createTime: number): ("塑形" | "妆容" | "全脸")[] {
  if (isLegacyData(createTime)) {
    // 历史数据不能单独应用塑形，移除塑形部位
    const availableParts = allowedParts & ~FACE_PARTS.SHAPE;
    return getPartsDisplayNames(availableParts);
  }
  
  return getPartsDisplayNames(allowedParts);
}

/**
 * 检查请求的部位是否被作品允许
 * @param allowedParts 作品允许的部位
 * @param requestedParts 请求的部位
 * @returns 是否被允许
 */
export function isPartsAllowed(allowedParts: number, requestedParts: number): boolean {
  return (allowedParts & requestedParts) === requestedParts;
}

/**
 * 检查历史数据的部位使用限制
 * @param createTime 作品创建时间
 * @param requestedParts 请求的部位
 * @returns 是否违反历史数据限制
 */
export function violatesLegacyDataRestriction(createTime: number, requestedParts: number): boolean {
  return isLegacyData(createTime) && (requestedParts & FACE_PARTS.SHAPE) > 0;
}