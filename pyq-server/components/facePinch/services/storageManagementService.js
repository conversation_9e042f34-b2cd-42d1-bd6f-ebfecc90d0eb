"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StorageManagementService = void 0;
const collect_1 = require("../models/collect");
const work_1 = require("../models/work");
const errorCode_1 = require("../errorCode");
const config_1 = require("../../../common/config");
class StorageManagementService {
    /**
     * 检查用户的存储容量
     * @param userId 用户ID
     * @returns 存储状态
     */
    static checkStorageCapacity(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const [collectCount, selfWorkCount] = yield Promise.all([
                StorageManagementService.getCollectCount(userId),
                StorageManagementService.getSelfWorkCount(userId)
            ]);
            const totalCount = collectCount + selfWorkCount;
            return {
                used: totalCount,
                total: StorageManagementService.MAX_STORAGE_COUNT,
                available: StorageManagementService.MAX_STORAGE_COUNT - totalCount,
                canAdd: totalCount < StorageManagementService.MAX_STORAGE_COUNT
            };
        });
    }
    /**
     * 获取用户收藏数量
     * @param userId 用户ID
     * @returns 收藏数量
     */
    static getCollectCount(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield collect_1.CollectModel.count({
                userId,
                status: 0 // 0表示有效收藏
            });
        });
    }
    /**
     * 获取用户自己的设计作品数量
     * @param userId 用户ID
     * @returns 作品数量
     */
    static getSelfWorkCount(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            return yield work_1.WorkModel.count({
                userId,
                status: 0 // 0表示正常状态
            });
        });
    }
    /**
     * 检查是否可以添加收藏
     * @param userId 用户ID
     * @returns 是否可以添加
     */
    static canAddToCollection(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const capacity = yield StorageManagementService.checkStorageCapacity(userId);
            return capacity.canAdd;
        });
    }
    /**
     * 检查是否可以上传新作品
     * @param userId 用户ID
     * @returns 是否可以上传
     */
    static canUploadWork(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const capacity = yield StorageManagementService.checkStorageCapacity(userId);
            return capacity.canAdd;
        });
    }
    /**
     * 在添加收藏前进行容量检查，如果超限则抛出错误
     * @param userId 用户ID
     */
    static validateCollectionCapacity(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const canAdd = yield StorageManagementService.canAddToCollection(userId);
            if (!canAdd) {
                throw errorCode_1.ApiErrors.StorageLimitExceeded;
            }
        });
    }
    /**
     * 在上传作品前进行容量检查，如果超限则抛出错误
     * @param userId 用户ID
     */
    static validateUploadCapacity(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const canAdd = yield StorageManagementService.canUploadWork(userId);
            if (!canAdd) {
                throw errorCode_1.ApiErrors.StorageLimitExceeded;
            }
        });
    }
    /**
     * 获取容量使用情况的格式化字符串
     * @param userId 用户ID
     * @returns 格式化的容量信息（如：285/300）
     */
    static getCapacityDisplay(userId) {
        return __awaiter(this, void 0, void 0, function* () {
            const capacity = yield StorageManagementService.checkStorageCapacity(userId);
            return `${capacity.used}/${capacity.total}`;
        });
    }
}
exports.StorageManagementService = StorageManagementService;
/** 收藏和我的设计的总存储上限 */
StorageManagementService.MAX_STORAGE_COUNT = config_1.facePinchCfg.maxStorageCount || 300;
//# sourceMappingURL=storageManagementService.js.map