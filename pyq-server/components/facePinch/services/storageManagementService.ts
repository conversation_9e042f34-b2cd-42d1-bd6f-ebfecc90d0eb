import { CollectModel } from '../models/collect';
import { WorkModel } from '../models/work';
import { ApiErrors } from '../errorCode';
import { facePinchCfg } from '../../../common/config';

export interface StorageStatus {
  used: number;
  total: number;
  available: number;
  canAdd: boolean;
}

export class StorageManagementService {
  /** 收藏和我的设计的总存储上限 */
  static readonly MAX_STORAGE_COUNT = facePinchCfg.maxStorageCount || 300;

  /**
   * 检查用户的存储容量
   * @param userId 用户ID
   * @returns 存储状态
   */
  static async checkStorageCapacity(userId: number): Promise<StorageStatus> {
    const [collectCount, selfWorkCount] = await Promise.all([
      StorageManagementService.getCollectCount(userId),
      StorageManagementService.getSelfWorkCount(userId)
    ]);

    const totalCount = collectCount + selfWorkCount;

    return {
      used: totalCount,
      total: StorageManagementService.MAX_STORAGE_COUNT,
      available: StorageManagementService.MAX_STORAGE_COUNT - totalCount,
      canAdd: totalCount < StorageManagementService.MAX_STORAGE_COUNT
    };
  }

  /**
   * 获取用户收藏数量
   * @param userId 用户ID
   * @returns 收藏数量
   */
  static async getCollectCount(userId: number): Promise<number> {
    return await CollectModel.count({
      userId,
      status: 0 // 0表示有效收藏
    });
  }

  /**
   * 获取用户自己的设计作品数量
   * @param userId 用户ID
   * @returns 作品数量
   */
  static async getSelfWorkCount(userId: number): Promise<number> {
    return await WorkModel.count({
      userId,
      status: 0 // 0表示正常状态
    });
  }

  /**
   * 检查是否可以添加收藏
   * @param userId 用户ID
   * @returns 是否可以添加
   */
  static async canAddToCollection(userId: number): Promise<boolean> {
    const capacity = await StorageManagementService.checkStorageCapacity(userId);
    return capacity.canAdd;
  }

  /**
   * 检查是否可以上传新作品
   * @param userId 用户ID
   * @returns 是否可以上传
   */
  static async canUploadWork(userId: number): Promise<boolean> {
    const capacity = await StorageManagementService.checkStorageCapacity(userId);
    return capacity.canAdd;
  }

  /**
   * 在添加收藏前进行容量检查，如果超限则抛出错误
   * @param userId 用户ID
   */
  static async validateCollectionCapacity(userId: number): Promise<void> {
    const canAdd = await StorageManagementService.canAddToCollection(userId);
    if (!canAdd) {
      throw ApiErrors.StorageLimitExceeded;
    }
  }

  /**
   * 在上传作品前进行容量检查，如果超限则抛出错误
   * @param userId 用户ID
   */
  static async validateUploadCapacity(userId: number): Promise<void> {
    const canAdd = await StorageManagementService.canUploadWork(userId);
    if (!canAdd) {
      throw ApiErrors.StorageLimitExceeded;
    }
  }

  /**
   * 获取容量使用情况的格式化字符串
   * @param userId 用户ID
   * @returns 格式化的容量信息（如：285/300）
   */
  static async getCapacityDisplay(userId: number): Promise<string> {
    const capacity = await StorageManagementService.checkStorageCapacity(userId);
    return `${capacity.used}/${capacity.total}`;
  }
}