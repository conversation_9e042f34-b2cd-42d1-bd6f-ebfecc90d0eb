import { validatePartsSelection } from '../utils/partsUtils';
import type { QueryBuilder } from "knex";

export interface FilterParams {
  filterParts?: number;
  page?: number;
  pageSize?: number;
  [key: string]: any;
}

export class FacePartFilterService {
  /**
   * 根据筛选条件构建查询
   * @param baseQuery 基础查询对象
   * @param filterParts 筛选的部位
   * @returns 扩展后的查询对象
   */
  static buildFilterQuery(baseQuery: QueryBuilder, filterParts?: number): QueryBuilder {
    if (!filterParts || !validatePartsSelection(filterParts)) {
      return baseQuery;
    }

    // 性能优化：使用 IN 查询替代位运算，可以利用索引
    // 找出所有包含指定部位的 allowedParts 值
    const validAllowedParts = this.getValidAllowedPartsValues(filterParts);
    baseQuery.whereIn('allowedParts', validAllowedParts);

    return baseQuery;
  }

  /**
   * 获取包含指定部位的所有有效 allowedParts 值
   * @param filterParts 筛选的部位
   * @returns 有效的 allowedParts 值数组
   */
  static getValidAllowedPartsValues(filterParts: number): number[] {
    const validValues: number[] = [];

    // 遍历所有可能的 allowedParts 值 (1-7)
    for (let allowedParts = 1; allowedParts <= 7; allowedParts++) {
      // 检查是否包含所有筛选的部位
      if ((allowedParts & filterParts) === filterParts) {
        validValues.push(allowedParts);
      }
    }

    return validValues;
  }

  /**
   * 验证部位筛选参数
   * @param filterParts 筛选条件
   * @returns 验证结果
   */
  static validateFilterParts(filterParts?: number): { valid: boolean; error?: string } {
    if (filterParts === undefined) {
      return { valid: true };
    }

    if (!validatePartsSelection(filterParts)) {
      return {
        valid: false,
        error: '无效的部位筛选条件'
      };
    }

    return { valid: true };
  }

  /**
   * 检查作品是否匹配筛选条件
   * @param allowedParts 作品允许的部位
   * @param filterParts 筛选条件
   * @returns 是否匹配
   */
  static matchesFilter(allowedParts: number, filterParts?: number): boolean {
    if (!filterParts) {
      return true;
    }

    // 检查部位是否匹配
    return (allowedParts & filterParts) === filterParts;
  }
}