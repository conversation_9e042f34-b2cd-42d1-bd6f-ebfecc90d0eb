import { operations } from "../../types/swagger"

export namespace FacePinchReq {

  export type WebPublicShareInfo = operations["facePinchWebPublicShareInfo"]["parameters"]["query"]
  export type DashenShareInfoImport = operations["facePinchDashenShareInfoImport"]["parameters"]["query"]
  export type WebPublicShareVideoInfo = operations["facePinchWebPublicShareVideoInfo"]["parameters"]["query"]
  export type ShareVideoAdd = operations["facePinchShareVideoAdd"]["parameters"]["query"]
  export type CloudGameAuthLogin = operations["facePinchCloudGameAuthLogin"]["parameters"]["query"]
  export type WorkListPublic = operations["facePinchWorkListPublic"]["parameters"]["query"]
  export type WorkListCollect = operations["facePinchWorkListCollect"]["parameters"]["query"]
  export type WorkListSelf = operations["facePinchWorkListSelf"]["parameters"]["query"]
  export type WorkGetByShareId = operations["facePinchWorkGetByShareId"]["parameters"]["query"]
  export type WorkDetail = operations["facePinchWorkDetail"]["parameters"]["query"]
  export type GetFpToken = operations["facePinchGetFpToken"]["parameters"]["query"]
  export type WorkSearch = operations["facePinchWorkSearch"]["parameters"]["query"]
  export type WorkAdd = operations["facePinchWorkAdd"]["parameters"]["query"] &
    operations["facePinchWorkAdd"]["requestBody"]["content"]["application/json"]
  export type WorkUpdateVisibility = operations["facePinchWorkUpdateVisibility"]["parameters"]["query"]
  export type WorkDel = operations["facePinchWorkDel"]["parameters"]["query"]
  export type WorkApply = operations["facePinchWorkApply"]["parameters"]["query"]
  export type WorkLike = operations["facePinchWorkLike"]["parameters"]["query"]
  export type WorkCancelLike = operations["facePinchWorkCancelLike"]["parameters"]["query"]
  export type WorkCollect = operations["facePinchWorkCollect"]["parameters"]["query"]
  export type WorkCancelCollect = operations["facePinchWorkCancelCollect"]["parameters"]["query"]
}

export namespace FacePinchRes {
  export type WebPublicShareInfo =
    operations["facePinchWebPublicShareInfo"]["responses"]["200"]["content"]["application/json"]["data"]

  export type DashenShareInfoImport =
    operations["facePinchDashenShareInfoImport"]["responses"]["200"]["content"]["application/json"]["data"]

  export type WebPublicShareVideoInfo =
    operations["facePinchWebPublicShareVideoInfo"]["responses"]["200"]["content"]["application/json"]["data"]
  export type ShareVideoAdd =
    operations["facePinchShareVideoAdd"]["responses"]["200"]["content"]["application/json"]["data"]
  export type CloudGameAuthLogin =
    operations["facePinchCloudGameAuthLogin"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkListPublic =
    operations["facePinchWorkListPublic"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkListCollect =
    operations["facePinchWorkListCollect"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkListSelf =
    operations["facePinchWorkListSelf"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkGetByShareId =
    operations["facePinchWorkGetByShareId"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkDetail = operations["facePinchWorkDetail"]["responses"]["200"]["content"]["application/json"]["data"]
  export type GetFpToken = operations["facePinchGetFpToken"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkSearch = operations["facePinchWorkSearch"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkAdd = operations["facePinchWorkAdd"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkUpdateVisibility = operations["facePinchWorkUpdateVisibility"]["responses"]["200"]["content"]["application/json"]["data"]

  export type WorkDel = operations["facePinchWorkDel"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkApply = operations["facePinchWorkApply"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkLike = operations["facePinchWorkLike"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkCancelLike =
    operations["facePinchWorkCancelLike"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkCollect =
    operations["facePinchWorkCollect"]["responses"]["200"]["content"]["application/json"]["data"]
  export type WorkCancelCollect =
    operations["facePinchWorkCancelCollect"]["responses"]["200"]["content"]["application/json"]["data"]
}

export const ReqSchemas = {
  FacePinchWebPublicShareInfo: {
    type: "object",
    properties: {
      shareId: { type: "string" },
    },
    required: ["shareId"],
  },
  FacePinchDashenShareInfoImport: {
    type: "object",
    properties: {
      shareId: { type: "string" },
      roleId: { type: "number" },
      server: { type: "number" },
    },
    required: ["shareId", "roleId", "server"],
  },
  FacePinchWebPublicShareVideoInfo: {
    type: "object",
    properties: {
      shareId: { type: "string" },
    },
    required: ["shareId"],
  },
  FacePinchShareVideoAdd: {
    type: "object",
    properties: {
      video: { type: "string" },
    },
    required: ["video"],
  },
  FacePinchCloudGameAuthLogin: {
    type: "object",
    properties: {
      roleId: { type: "number" },
      account: { type: "string" },
      time: { type: "number" },
      token: { type: "string" },
    },
    required: ["account", "time", "token"],
  },
  FacePinchWorkListPublic: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      skey: { type: "string" },
      gender: { type: "number" },
      bodyShape: { type: "number", enum: [0, 1, 2, 3] },
      jobId: { type: "number" },
      page: { type: "number", default: 1 },
      pageSize: { type: "number", default: 10 },
      filterParts: { type: "number", minimum: 1, maximum: 7 },
    },
    required: ["roleid", "skey"],
  },
  FacePinchWorkListCollect: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      skey: { type: "string" },
      page: { type: "number", default: 1 },
      pageSize: { type: "number", default: 10 },
      filterParts: { type: "number", minimum: 1, maximum: 7 },
    },
    required: ["roleid", "skey"],
  },
  FacePinchWorkListSelf: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      skey: { type: "string" },
      page: { type: "number", default: 1 },
      pageSize: { type: "number", default: 10 },
      filterParts: { type: "number", minimum: 1, maximum: 7 },
    },
    required: ["roleid", "skey"],
  },
  FacePinchWorkGetByShareId: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, shareId: { type: "string" } },
    required: ["roleid", "skey", "shareId"],
  },
  FacePinchWorkDetail: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
    required: ["roleid", "skey", "id"],
  },
  FacePinchWorkSearch: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      skey: { type: "string" },
      kw: { type: "string", maxLength: 7, minLength: 2 },
      gender: { type: "number" },
      jobId: { type: "number" },
      bodyShape: { type: "number", enum: [0, 1, 2, 3] },
    },
    required: ["roleid", "skey", "kw"],
  },
  FacePinchWorkAdd: {
    type: "object",
    properties: {
      roleid: { type: "number" },
      skey: { type: "string" },
      dataUrl: { type: "string" },
      image: { type: "string" },
      title: { type: "string" },
      desc: { type: "string" },
      roleName: { type: "string" },
      gender: { type: "number" },
      visibility: { type: "number", enum: [0, 1] },
      bodyShape: { type: "number", enum: [0, 1, 2, 3] },
      jobId: { type: "number" },
      allowedParts: { type: "number", minimum: 1, maximum: 7, default: 7 },
    },
    required: ["roleid", "skey", "dataUrl", "image", "title", "roleName", "gender", "jobId", "allowedParts"],
  },

  FacePinchWorkUpdateVisibility: {
    type: "object",
    properties: { roleid: { type: "number" }, id: { type: "number" }, visibility: {type: "number", enum: [0, 1]} },
    required: ["roleid", "id", "visibility"],
  },

  FacePinchGetFpToken: {
    type: "object",
    properties: { roleid: { type: "number" } },
    required: ["roleid"],
  },

  FacePinchWorkDel: {
    type: "object",
    properties: { roleid: { type: "number" }, id: { type: "number" } },
    required: ["roleid", "id"],
  },

  FacePinchWorkApply: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
    required: ["roleid", "skey", "id"],
  },
  FacePinchWorkLike: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
    required: ["roleid", "skey", "id"],
  },
  FacePinchWorkCancelLike: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
    required: ["roleid", "skey", "id"],
  },
  FacePinchWorkCollect: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
    required: ["roleid", "skey", "id"],
  },
  FacePinchWorkCancelCollect: {
    type: "object",
    properties: { roleid: { type: "number" }, skey: { type: "string" }, id: { type: "number" } },
    required: ["roleid", "skey", "id"],
  },
}